// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../auxiliary/sample_types.dart';
import '../frb_generated.dart';
import 'misc_no_twin_example_b.dart';
import 'package:collection/collection.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
part 'misc_no_twin_example_a.freezed.dart';part 'misc_no_twin_example_a.g.dart';

            // These functions are ignored because they are not marked as `pub`: `CONST_PRIVATE_SHOULD_IGNORE`, `CONST_PUB_CRATE_SHOULD_IGNORE`, `log`
// These types are ignored because they are neither used by any `pub` functions nor (for structs and enums) marked `#[frb(unignore)]`: `Issue2170Struct`
// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `clone`, `clone`, `clone`, `clone`, `clone`, `fmt`, `fmt`, `fmt`, `fmt`
// These functions are ignored (category: IgnoreBecauseExplicitAttribute): `CONST_WITH_EXPLICIT_IGNORE_SHOULD_IGNORE`


            Future<void>  sameFunctionNameInDifferentFiles() => RustLib.instance.api.crateApiMiscNoTwinExampleASameFunctionNameInDifferentFiles();

Future<void>  renamedFunction() => RustLib.instance.api.crateApiMiscNoTwinExampleAFunctionWithCustomNameTwinNormal();

Future<void>  functionWithArgTypeNameOverride({required BoxAnyMyDartTypeRename a }) => RustLib.instance.api.crateApiMiscNoTwinExampleAFunctionWithArgTypeNameOverride(a: a);

Future<String>  featureGatedFunction() => RustLib.instance.api.crateApiMiscNoTwinExampleAFeatureGatedFunction();

Future<void>  for_({required String type }) => RustLib.instance.api.crateApiMiscNoTwinExampleAFor(type: type);

int get constIntTwinNormal => RustLib.instance.api.crateApiMiscNoTwinExampleAConstIntTwinNormal();

F32Array3 get constArrayTwinNormal => RustLib.instance.api.crateApiMiscNoTwinExampleAConstArrayTwinNormal();

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn Any + Send + Sync + 'static >>>
                abstract class BoxAnyMyDartTypeRename implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<DeliberateFailSanityCheckTwinNormal>>
                abstract class DeliberateFailSanityCheckTwinNormal implements RustOpaqueInterface {
                     Uint8List get deliberateBadFieldA;


 OpaqueItemTwinNormal get deliberateBadFieldB;


 List<OpaqueItemTwinNormal> get deliberateBadFieldC;


 String get goodFieldA;


 int get goodFieldB;


 OpaqueItemTwinNormal get goodFieldC;


  set deliberateBadFieldA(Uint8List deliberateBadFieldA);


  set deliberateBadFieldB(OpaqueItemTwinNormal deliberateBadFieldB);


  set deliberateBadFieldC(List<OpaqueItemTwinNormal> deliberateBadFieldC);


  set goodFieldA(String goodFieldA);


  set goodFieldB(int goodFieldB);


  set goodFieldC(OpaqueItemTwinNormal goodFieldC);


static Future<void>  dummyFunctionTwinNormal()=>RustLib.instance.api.crateApiMiscNoTwinExampleADeliberateFailSanityCheckTwinNormalDummyFunctionTwinNormal();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<ItemContainerSolutionOneTwinNormal>>
                abstract class ItemContainerSolutionOneTwinNormal implements RustOpaqueInterface {
                     String get name;


  set name(String name);


static Future<ItemContainerSolutionOneTwinNormal>  createTwinNormal()=>RustLib.instance.api.crateApiMiscNoTwinExampleAItemContainerSolutionOneTwinNormalCreateTwinNormal();


 Future<Int32List>  getItemContentsTwinNormal();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MyStructWithTryFromTwinNormal>>
                abstract class MyStructWithTryFromTwinNormal implements RustOpaqueInterface {
                    static Future<MyStructWithTryFromTwinNormal>  tryFrom({required String value })=>RustLib.instance.api.crateApiMiscNoTwinExampleAMyStructWithTryFromTwinNormalTryFrom(value: value);


 Future<String>  valueTwinNormal();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueItemTwinNormal>>
                abstract class OpaqueItemTwinNormal implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<SimpleLogger>>
                abstract class SimpleLogger implements RustOpaqueInterface {
                     List<String>  getAndReset();


factory SimpleLogger()=>RustLib.instance.api.crateApiMiscNoTwinExampleASimpleLoggerNew();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructInMiscNoTwinExampleA>>
                abstract class StructInMiscNoTwinExampleA implements RustOpaqueInterface {
                     Future<void>  sampleFunctionA();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithImplBlockInMultiFile>>
                abstract class StructWithImplBlockInMultiFile implements RustOpaqueInterface {
                     Future<void>  methodInA();


 Future<void>  methodInB();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithRustAutoOpaqueFieldWithManyDeriveInner>>
                abstract class StructWithRustAutoOpaqueFieldWithManyDeriveInner implements RustOpaqueInterface {
                    static Future<StructWithRustAutoOpaqueFieldWithManyDeriveInner>  default_()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithRustAutoOpaqueFieldWithManyDeriveInnerDefault();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithSimpleSetterTwinNormal>>
                abstract class StructWithSimpleSetterTwinNormal implements RustOpaqueInterface {
                     int get something;


factory StructWithSimpleSetterTwinNormal()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithSimpleSetterTwinNormalNew();


  set something(int value);


 int get simpleGetter;


  set simpleSetter(int value);



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<TypeForIgnore>>
                abstract class TypeForIgnore implements RustOpaqueInterface {
                    static Future<TypeForIgnore>  default_()=>RustLib.instance.api.crateApiMiscNoTwinExampleATypeForIgnoreDefault();


 Future<int>  field1();


  // HINT: Make it `#[frb(sync)]` to let it become the default constructor of Dart class.
static Future<TypeForIgnore>  newInstance()=>RustLib.instance.api.crateApiMiscNoTwinExampleATypeForIgnoreNew();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<TypeForIgnoreAll>>
                abstract class TypeForIgnoreAll implements RustOpaqueInterface {
                     int get field2;


  set field2(int field2);


static Future<TypeForIgnoreAll>  default_()=>RustLib.instance.api.crateApiMiscNoTwinExampleATypeForIgnoreAllDefault();


 Future<int>  field1();


  // HINT: Make it `#[frb(sync)]` to let it become the default constructor of Dart class.
static Future<TypeForIgnoreAll>  newInstance()=>RustLib.instance.api.crateApiMiscNoTwinExampleATypeForIgnoreAllNew();



                    
                }
                


                abstract class Issue2170Trait {
                     Future<void>  method();


                }
                


            class F32Array3 extends NonGrowableListView<double> {
                static const arraySize = 3;

                @internal
                Float32List get inner => _inner;
                final Float32List _inner;

                F32Array3(this._inner)
                    : assert(_inner.length == arraySize),
                      super(_inner);
  
                F32Array3.init(): this(Float32List(arraySize));
              }
            

class ItemContainerSolutionTwoTwinNormal  {
                 String name;
final List<OpaqueItemTwinNormal> items;

                ItemContainerSolutionTwoTwinNormal({required this.name ,required this.items ,});

                static Future<ItemContainerSolutionTwoTwinNormal>  createTwinNormal()=>RustLib.instance.api.crateApiMiscNoTwinExampleAItemContainerSolutionTwoTwinNormalCreateTwinNormal();


 Future<Int32List>  getItemContentsTwinNormal()=>RustLib.instance.api.crateApiMiscNoTwinExampleAItemContainerSolutionTwoTwinNormalGetItemContentsTwinNormal(that: this, );


                

                
        @override
        int get hashCode => name.hashCode^items.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is ItemContainerSolutionTwoTwinNormal &&
                runtimeType == other.runtimeType
                && name == other.name&& items == other.items;
        
            }

@freezed
                sealed class MyEnumWithJsonSerializableTwinNormal with _$MyEnumWithJsonSerializableTwinNormal  {
                    const MyEnumWithJsonSerializableTwinNormal._();

                     const factory MyEnumWithJsonSerializableTwinNormal.apple(  String field0,) = MyEnumWithJsonSerializableTwinNormal_Apple;
 const factory MyEnumWithJsonSerializableTwinNormal.orange({   required int a , }) = MyEnumWithJsonSerializableTwinNormal_Orange;

                    factory MyEnumWithJsonSerializableTwinNormal.fromJson(Map<String, dynamic> json) => _$MyEnumWithJsonSerializableTwinNormalFromJson(json);

                     Future<void>  f()=>RustLib.instance.api.crateApiMiscNoTwinExampleAMyEnumWithJsonSerializableTwinNormalF(that: this, );


                }

@freezed
                sealed class MyEnumWithoutFnWithUnignoreTwinNormal with _$MyEnumWithoutFnWithUnignoreTwinNormal  {
                    const MyEnumWithoutFnWithUnignoreTwinNormal._();

                     const factory MyEnumWithoutFnWithUnignoreTwinNormal.one(  String field0,) = MyEnumWithoutFnWithUnignoreTwinNormal_One;

                    

                    
                }

@freezed
sealed class MyStructWithJsonSerializableTwinNormal with _$MyStructWithJsonSerializableTwinNormal  {
                const MyStructWithJsonSerializableTwinNormal._();
                const factory MyStructWithJsonSerializableTwinNormal({ required  String fieldOne,}) = _MyStructWithJsonSerializableTwinNormal;
                 Future<void>  f()=>RustLib.instance.api.crateApiMiscNoTwinExampleAMyStructWithJsonSerializableTwinNormalF(that: this, );


                factory MyStructWithJsonSerializableTwinNormal.fromJson(Map<String, dynamic> json) => _$MyStructWithJsonSerializableTwinNormalFromJson(json);
                
            }

class MyStructWithSync  {
                

                const MyStructWithSync();

                 Future<void>  sync()=>RustLib.instance.api.crateApiMiscNoTwinExampleAMyStructWithSyncSync(that: this, );


                

                
        @override
        int get hashCode => 0;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is MyStructWithSync &&
                runtimeType == other.runtimeType
                ;
        
            }

class MyStructWithoutFnWithUnignoreTwinNormal  {
                final String a;

                const MyStructWithoutFnWithUnignoreTwinNormal({required this.a ,});

                
                

                
        @override
        int get hashCode => a.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is MyStructWithoutFnWithUnignoreTwinNormal &&
                runtimeType == other.runtimeType
                && a == other.a;
        
            }

@freezed
sealed class MyStructWithoutFnWithUnignoreWithJsonSerializableTwinNormal with _$MyStructWithoutFnWithUnignoreWithJsonSerializableTwinNormal  {
                
                const factory MyStructWithoutFnWithUnignoreWithJsonSerializableTwinNormal({ required  String a,}) = _MyStructWithoutFnWithUnignoreWithJsonSerializableTwinNormal;
                
                factory MyStructWithoutFnWithUnignoreWithJsonSerializableTwinNormal.fromJson(Map<String, dynamic> json) => _$MyStructWithoutFnWithUnignoreWithJsonSerializableTwinNormalFromJson(json);
                
            }

class StructWithCustomNameMethodTwinNormal  {
                final int field0;

                const StructWithCustomNameMethodTwinNormal({required this.field0 ,});

                 void  renamedMethod()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithCustomNameMethodTwinNormalMethodWithCustomNameTwinNormal(that: this, );


                

                
        @override
        int get hashCode => field0.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithCustomNameMethodTwinNormal &&
                runtimeType == other.runtimeType
                && field0 == other.field0;
        
            }

class StructWithImplBlockInAnotherFile  {
                

                const StructWithImplBlockInAnotherFile();

                static Future<void>  f({required StructWithImplBlockInAnotherFileDependency arg })=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithImplBlockInAnotherFileF(arg: arg);


                

                
        @override
        int get hashCode => 0;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithImplBlockInAnotherFile &&
                runtimeType == other.runtimeType
                ;
        
            }

class StructWithRawNameField  {
                final String type;

                const StructWithRawNameField({required this.type ,});

                static Future<void>  dummyFunction()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithRawNameFieldDummyFunction();


                

                
        @override
        int get hashCode => type.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithRawNameField &&
                runtimeType == other.runtimeType
                && type == other.type;
        
            }

class StructWithRustAutoOpaqueFieldWithManyDerive  {
                final StructWithRustAutoOpaqueFieldWithManyDeriveInner content;

                const StructWithRustAutoOpaqueFieldWithManyDerive({required this.content ,});

                static Future<StructWithRustAutoOpaqueFieldWithManyDerive>  default_()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithRustAutoOpaqueFieldWithManyDeriveDefault();


 Future<void>  f()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithRustAutoOpaqueFieldWithManyDeriveF(that: this, );


                

                
        @override
        int get hashCode => content.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithRustAutoOpaqueFieldWithManyDerive &&
                runtimeType == other.runtimeType
                && content == other.content;
        
            }

class StructWithRustAutoOpaqueWithNonCloneData  {
                final NonCloneDataRaw content;

                const StructWithRustAutoOpaqueWithNonCloneData({required this.content ,});

                 Future<void>  f()=>RustLib.instance.api.crateApiMiscNoTwinExampleAStructWithRustAutoOpaqueWithNonCloneDataF(that: this, );


                

                
        @override
        int get hashCode => content.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithRustAutoOpaqueWithNonCloneData &&
                runtimeType == other.runtimeType
                && content == other.content;
        
            }
            