// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_sync_moi.freezed.dart';

            

            void  rustAutoOpaqueArgOwnTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueArgOwnTwinSyncMoi(arg: arg, expect: expect);

void  rustAutoOpaqueArgBorrowTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueArgBorrowTwinSyncMoi(arg: arg, expect: expect);

void  rustAutoOpaqueArgMutBorrowTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueArgMutBorrowTwinSyncMoi(arg: arg, expect: expect, adder: adder);

NonCloneSimpleTwinSyncMoi  rustAutoOpaqueReturnOwnTwinSyncMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueReturnOwnTwinSyncMoi(initial: initial);

NonCloneSimpleTwinSyncMoi  rustAutoOpaqueArgOwnAndReturnOwnTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueArgOwnAndReturnOwnTwinSyncMoi(arg: arg);

void  rustAutoOpaqueTwoArgsTwinSyncMoi({required NonCloneSimpleTwinSyncMoi a , required NonCloneSimpleTwinSyncMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueTwoArgsTwinSyncMoi(a: a, b: b);

void  rustAutoOpaqueNormalAndOpaqueArgTwinSyncMoi({required NonCloneSimpleTwinSyncMoi a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueNormalAndOpaqueArgTwinSyncMoi(a: a, b: b);

/// "+" inside the type signature
void  rustAutoOpaquePlusSignArgTwinSyncMoi({required BoxMyTraitTwinSyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaquePlusSignArgTwinSyncMoi(arg: arg);

BoxMyTraitTwinSyncMoi  rustAutoOpaquePlusSignReturnTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaquePlusSignReturnTwinSyncMoi();

void  rustAutoOpaqueCallableArgTwinSyncMoi({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueCallableArgTwinSyncMoi(arg: arg);

BoxFnStringString  rustAutoOpaqueCallableReturnTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueCallableReturnTwinSyncMoi();

void  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSyncMoi({required StructWithGoodAndOpaqueFieldTwinSyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSyncMoi(arg: arg);

StructWithGoodAndOpaqueFieldTwinSyncMoi  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSyncMoi();

void  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSyncMoi({required EnumWithGoodAndOpaqueTwinSyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSyncMoi(arg: arg);

EnumWithGoodAndOpaqueTwinSyncMoi  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSyncMoi();

EnumWithGoodAndOpaqueTwinSyncMoi  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSyncMoi();

void  rustAutoOpaqueDummyTwinSyncMoi({required StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncMoi a , required EnumWithGoodAndOpaqueWithoutOptionTwinSyncMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueDummyTwinSyncMoi(a: a, b: b);

void  rustAutoOpaqueEnumArgBorrowTwinSyncMoi({required NonCloneSimpleEnumTwinSyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueEnumArgBorrowTwinSyncMoi(arg: arg);

NonCloneSimpleEnumTwinSyncMoi  rustAutoOpaqueEnumReturnOwnTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueEnumReturnOwnTwinSyncMoi();

Stream<NonCloneSimpleTwinSyncMoi>  rustAutoOpaqueStreamSinkTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueStreamSinkTwinSyncMoi();

void  rustAutoOpaqueArgVecOwnTwinSyncMoi({required List<NonCloneSimpleTwinSyncMoi> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueArgVecOwnTwinSyncMoi(arg: arg, expect: expect);

List<NonCloneSimpleTwinSyncMoi>  rustAutoOpaqueReturnVecOwnTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueReturnVecOwnTwinSyncMoi();

void  rustAutoOpaqueExplicitArgTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueExplicitArgTwinSyncMoi(arg: arg, expect: expect);

void  rustAutoOpaqueExplicitStructTwinSyncMoi({required StructWithExplicitAutoOpaqueFieldTwinSyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueExplicitStructTwinSyncMoi(arg: arg);

StructWithExplicitAutoOpaqueFieldTwinSyncMoi  rustAutoOpaqueExplicitReturnStructTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueExplicitReturnStructTwinSyncMoi();

NonCloneSimpleTwinSyncMoi  rustAutoOpaqueExplicitReturnTwinSyncMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueExplicitReturnTwinSyncMoi(initial: initial);

int  rustAutoOpaqueSleepTwinSyncMoi({required NonCloneSimpleTwinSyncMoi apple , required NonCloneSimpleTwinSyncMoi orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueSleepTwinSyncMoi(apple: apple, orange: orange);

(OpaqueOneTwinSyncMoi,OpaqueTwoTwinSyncMoi)  rustAutoOpaqueReturnOpaqueOneAndTwoTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueReturnOpaqueOneAndTwoTwinSyncMoi();

OpaqueTwoTwinSyncMoi  rustAutoOpaqueReturnOpaqueTwoTwinSyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueReturnOpaqueTwoTwinSyncMoi();

int  rustAutoOpaqueBorrowAndMutBorrowTwinSyncMoi({required NonCloneSimpleTwinSyncMoi borrow , required NonCloneSimpleTwinSyncMoi mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueBorrowAndMutBorrowTwinSyncMoi(borrow: borrow, mutBorrow: mutBorrow);

int  rustAutoOpaqueBorrowAndBorrowTwinSyncMoi({required NonCloneSimpleTwinSyncMoi a , required NonCloneSimpleTwinSyncMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiRustAutoOpaqueBorrowAndBorrowTwinSyncMoi(a: a, b: b);

            
                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinSyncMoi + Send + Sync >>>
                abstract class BoxMyTraitTwinSyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinSyncMoi>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinSyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinSyncMoi>>
                abstract class NonCloneSimpleEnumTwinSyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinSyncMoi>>
                abstract class NonCloneSimpleTwinSyncMoi implements RustOpaqueInterface {
                     void  instanceMethodArgBorrowTwinSyncMoi();


 void  instanceMethodArgMutBorrowTwinSyncMoi();


 void  instanceMethodArgOwnTwinSyncMoi();


 int get instanceMethodGetterTwinSyncMoi;


 NonCloneSimpleTwinSyncMoi  instanceMethodReturnOwnTwinSyncMoi();


/// named constructor
static NonCloneSimpleTwinSyncMoi  newCustomNameTwinSyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiNewCustomNameTwinSyncMoi();


/// unnamed constructor
static NonCloneSimpleTwinSyncMoi  newTwinSyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiNewTwinSyncMoi();


/// constructor with Result
static NonCloneSimpleTwinSyncMoi  newWithResultTwinSyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiNewWithResultTwinSyncMoi();


static void  staticMethodArgBorrowTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiStaticMethodArgBorrowTwinSyncMoi(arg: arg);


static void  staticMethodArgMutBorrowTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiStaticMethodArgMutBorrowTwinSyncMoi(arg: arg);


static void  staticMethodArgOwnTwinSyncMoi({required NonCloneSimpleTwinSyncMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiStaticMethodArgOwnTwinSyncMoi(arg: arg);


static NonCloneSimpleTwinSyncMoi  staticMethodReturnOwnTwinSyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncMoiNonCloneSimpleTwinSyncMoiStaticMethodReturnOwnTwinSyncMoi();



                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinSyncMoi>>
                abstract class OpaqueOneTwinSyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinSyncMoi>>
                abstract class OpaqueTwoTwinSyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncMoi>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncMoi implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinSyncMoi {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinSyncMoi with _$EnumWithGoodAndOpaqueTwinSyncMoi  {
                    const EnumWithGoodAndOpaqueTwinSyncMoi._();

                     const factory EnumWithGoodAndOpaqueTwinSyncMoi.good(  String field0,) = EnumWithGoodAndOpaqueTwinSyncMoi_Good;
 const factory EnumWithGoodAndOpaqueTwinSyncMoi.opaque(  NonCloneSimpleTwinSyncMoi field0,) = EnumWithGoodAndOpaqueTwinSyncMoi_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinSyncMoi  {
                final NonCloneSimpleTwinSyncMoi autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinSyncMoi({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinSyncMoi &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinSyncMoi  {
                final String good;
final NonCloneSimpleTwinSyncMoi opaque;
final NonCloneSimpleTwinSyncMoi? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinSyncMoi({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinSyncMoi &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            