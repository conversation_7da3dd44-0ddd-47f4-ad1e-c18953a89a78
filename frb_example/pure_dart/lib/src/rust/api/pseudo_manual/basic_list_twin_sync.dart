// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sync.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Int8List  exampleBasicListTypeI8TwinSync({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeI8TwinSync(arg: arg);

Int16List  exampleBasicListTypeI16TwinSync({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeI16TwinSync(arg: arg);

Int32List  exampleBasicListTypeI32TwinSync({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeI32TwinSync(arg: arg);

Int64List  exampleBasicListTypeI64TwinSync({required Int64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeI64TwinSync(arg: arg);

Uint8List  exampleBasicListTypeU8TwinSync({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeU8TwinSync(arg: arg);

Uint16List  exampleBasicListTypeU16TwinSync({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeU16TwinSync(arg: arg);

Uint32List  exampleBasicListTypeU32TwinSync({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeU32TwinSync(arg: arg);

Uint64List  exampleBasicListTypeU64TwinSync({required Uint64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeU64TwinSync(arg: arg);

Float32List  exampleBasicListTypeF32TwinSync({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeF32TwinSync(arg: arg);

Float64List  exampleBasicListTypeF64TwinSync({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeF64TwinSync(arg: arg);

List<bool>  exampleBasicListTypeBoolTwinSync({required List<bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeBoolTwinSync(arg: arg);

List<String>  exampleBasicListTypeStringTwinSync({required List<String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeStringTwinSync(arg: arg);

List<Uint8List>  exampleBasicListTypeBytesTwinSync({required List<Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeBytesTwinSync(arg: arg);

List<BasicPrimitiveEnumTwinSync>  exampleBasicListTypeBasicPrimitiveEnumTwinSyncTwinSync({required List<BasicPrimitiveEnumTwinSync> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeBasicPrimitiveEnumTwinSyncTwinSync(arg: arg);

List<BasicGeneralEnumTwinSync>  exampleBasicListTypeBasicGeneralEnumTwinSyncTwinSync({required List<BasicGeneralEnumTwinSync> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeBasicGeneralEnumTwinSyncTwinSync(arg: arg);

List<BasicStructTwinSync>  exampleBasicListTypeBasicStructTwinSyncTwinSync({required List<BasicStructTwinSync> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncExampleBasicListTypeBasicStructTwinSyncTwinSync(arg: arg);

            
            