// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../auxiliary/sample_types.dart';
import '../../frb_generated.dart';
import 'enumeration_twin_rust_async.dart';
import 'misc_example_twin_rust_async.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            // These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `build_hasher`, `clone`, `fmt`, `hash_one`
// These functions are ignored (category: IgnoreBecauseOwnerTyShouldIgnore): `default`


            Future<Map<int, int>>  funcHashMapI32I32TwinRustAsync({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapI32I32TwinRustAsync(arg: arg);

Future<Set<int>>  funcHashSetI32TwinRustAsync({required Set<int> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashSetI32TwinRustAsync(arg: arg);

Future<Map<String, String>>  funcHashMapStringStringTwinRustAsync({required Map<String, String> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapStringStringTwinRustAsync(arg: arg);

Future<Map<String, String>>  funcHashMapStringStringHasherTwinRustAsync({required Map<String, String> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapStringStringHasherTwinRustAsync(arg: arg);

Future<Set<String>>  funcHashSetStringTwinRustAsync({required Set<String> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashSetStringTwinRustAsync(arg: arg);

Future<Set<String>>  funcHashSetStringHasherTwinRustAsync({required Set<String> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashSetStringHasherTwinRustAsync(arg: arg);

Future<Map<String, Uint8List>>  funcHashMapStringBytesTwinRustAsync({required Map<String, Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapStringBytesTwinRustAsync(arg: arg);

Future<Map<String, MySize>>  funcHashMapStringStructTwinRustAsync({required Map<String, MySize> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapStringStructTwinRustAsync(arg: arg);

Future<Map<String, EnumSimpleTwinRustAsync>>  funcHashMapStringSimpleEnumTwinRustAsync({required Map<String, EnumSimpleTwinRustAsync> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapStringSimpleEnumTwinRustAsync(arg: arg);

Future<Map<String, KitchenSinkTwinRustAsync>>  funcHashMapStringComplexEnumTwinRustAsync({required Map<String, KitchenSinkTwinRustAsync> arg }) => RustLib.instance.api.crateApiPseudoManualMapAndSetTwinRustAsyncFuncHashMapStringComplexEnumTwinRustAsync(arg: arg);

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<CustomHasherTwinRustAsync>>
                abstract class CustomHasherTwinRustAsync implements RustOpaqueInterface {
                    

                    
                }
                
            