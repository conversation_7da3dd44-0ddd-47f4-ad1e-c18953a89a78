// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
part 'basic_twin_rust_async_sse.freezed.dart';

            

            Future<int>  exampleBasicTypeI8TwinRustAsyncSse({required int arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeI8TwinRustAsyncSse(arg: arg, expect: expect);

Future<int>  exampleBasicTypeI16TwinRustAsyncSse({required int arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeI16TwinRustAsyncSse(arg: arg, expect: expect);

Future<int>  exampleBasicTypeI32TwinRustAsyncSse({required int arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeI32TwinRustAsyncSse(arg: arg, expect: expect);

Future<PlatformInt64>  exampleBasicTypeI64TwinRustAsyncSse({required PlatformInt64 arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeI64TwinRustAsyncSse(arg: arg, expect: expect);

Future<BigInt>  exampleBasicTypeI128TwinRustAsyncSse({required BigInt arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeI128TwinRustAsyncSse(arg: arg, expect: expect);

Future<int>  exampleBasicTypeU8TwinRustAsyncSse({required int arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeU8TwinRustAsyncSse(arg: arg, expect: expect);

Future<int>  exampleBasicTypeU16TwinRustAsyncSse({required int arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeU16TwinRustAsyncSse(arg: arg, expect: expect);

Future<int>  exampleBasicTypeU32TwinRustAsyncSse({required int arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeU32TwinRustAsyncSse(arg: arg, expect: expect);

Future<BigInt>  exampleBasicTypeU64TwinRustAsyncSse({required BigInt arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeU64TwinRustAsyncSse(arg: arg, expect: expect);

Future<BigInt>  exampleBasicTypeU128TwinRustAsyncSse({required BigInt arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeU128TwinRustAsyncSse(arg: arg, expect: expect);

Future<PlatformInt64>  exampleBasicTypeIsizeTwinRustAsyncSse({required PlatformInt64 arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeIsizeTwinRustAsyncSse(arg: arg, expect: expect);

Future<BigInt>  exampleBasicTypeUsizeTwinRustAsyncSse({required BigInt arg , required String expect }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeUsizeTwinRustAsyncSse(arg: arg, expect: expect);

Future<double>  exampleBasicTypeF32TwinRustAsyncSse({required double arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeF32TwinRustAsyncSse(arg: arg);

Future<double>  exampleBasicTypeF64TwinRustAsyncSse({required double arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeF64TwinRustAsyncSse(arg: arg);

Future<bool>  exampleBasicTypeBoolTwinRustAsyncSse({required bool arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeBoolTwinRustAsyncSse(arg: arg);

Future<String>  exampleBasicTypeStringTwinRustAsyncSse({required String arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeStringTwinRustAsyncSse(arg: arg);

Future<Uint8List>  exampleBasicTypeBytesTwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeBytesTwinRustAsyncSse(arg: arg);

Future<BasicPrimitiveEnumTwinRustAsyncSse>  exampleBasicTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse({required BasicPrimitiveEnumTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<BasicGeneralEnumTwinRustAsyncSse>  exampleBasicTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse({required BasicGeneralEnumTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<BasicStructTwinRustAsyncSse>  exampleBasicTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse({required BasicStructTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualBasicTwinRustAsyncSseExampleBasicTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

            @freezed
                sealed class BasicGeneralEnumTwinRustAsyncSse with _$BasicGeneralEnumTwinRustAsyncSse  {
                    const BasicGeneralEnumTwinRustAsyncSse._();

                     const factory BasicGeneralEnumTwinRustAsyncSse.apple({   required String field , }) = BasicGeneralEnumTwinRustAsyncSse_Apple;
 const factory BasicGeneralEnumTwinRustAsyncSse.orange() = BasicGeneralEnumTwinRustAsyncSse_Orange;

                    

                    
                }

enum BasicPrimitiveEnumTwinRustAsyncSse {
                    apple,
orange,
                    ;
                    
                }

class BasicStructTwinRustAsyncSse  {
                final String? apple;
final int? orange;

                const BasicStructTwinRustAsyncSse({this.apple ,this.orange ,});

                
                

                
        @override
        int get hashCode => apple.hashCode^orange.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is BasicStructTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && apple == other.apple&& orange == other.orange;
        
            }
            