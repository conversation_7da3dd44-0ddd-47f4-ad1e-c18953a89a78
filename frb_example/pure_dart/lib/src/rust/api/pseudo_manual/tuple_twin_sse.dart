// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<(String,int)>  testTupleTwinSse({(String,int)? value }) => RustLib.instance.api.crateApiPseudoManualTupleTwinSseTestTupleTwinSse(value: value);

Future<void>  testTuple2TwinSse({required List<(String,int)> value }) => RustLib.instance.api.crateApiPseudoManualTupleTwinSseTestTuple2TwinSse(value: value);

            
            