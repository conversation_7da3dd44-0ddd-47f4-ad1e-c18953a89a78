// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sync_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Int8List  exampleBasicListTypeI8TwinSyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeI8TwinSyncSse(arg: arg);

Int16List  exampleBasicListTypeI16TwinSyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeI16TwinSyncSse(arg: arg);

Int32List  exampleBasicListTypeI32TwinSyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeI32TwinSyncSse(arg: arg);

Int64List  exampleBasicListTypeI64TwinSyncSse({required Int64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeI64TwinSyncSse(arg: arg);

Uint8List  exampleBasicListTypeU8TwinSyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeU8TwinSyncSse(arg: arg);

Uint16List  exampleBasicListTypeU16TwinSyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeU16TwinSyncSse(arg: arg);

Uint32List  exampleBasicListTypeU32TwinSyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeU32TwinSyncSse(arg: arg);

Uint64List  exampleBasicListTypeU64TwinSyncSse({required Uint64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeU64TwinSyncSse(arg: arg);

Float32List  exampleBasicListTypeF32TwinSyncSse({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeF32TwinSyncSse(arg: arg);

Float64List  exampleBasicListTypeF64TwinSyncSse({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeF64TwinSyncSse(arg: arg);

List<bool>  exampleBasicListTypeBoolTwinSyncSse({required List<bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeBoolTwinSyncSse(arg: arg);

List<String>  exampleBasicListTypeStringTwinSyncSse({required List<String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeStringTwinSyncSse(arg: arg);

List<Uint8List>  exampleBasicListTypeBytesTwinSyncSse({required List<Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeBytesTwinSyncSse(arg: arg);

List<BasicPrimitiveEnumTwinSyncSse>  exampleBasicListTypeBasicPrimitiveEnumTwinSyncSseTwinSyncSse({required List<BasicPrimitiveEnumTwinSyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeBasicPrimitiveEnumTwinSyncSseTwinSyncSse(arg: arg);

List<BasicGeneralEnumTwinSyncSse>  exampleBasicListTypeBasicGeneralEnumTwinSyncSseTwinSyncSse({required List<BasicGeneralEnumTwinSyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeBasicGeneralEnumTwinSyncSseTwinSyncSse(arg: arg);

List<BasicStructTwinSyncSse>  exampleBasicListTypeBasicStructTwinSyncSseTwinSyncSse({required List<BasicStructTwinSyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSyncSseExampleBasicListTypeBasicStructTwinSyncSseTwinSyncSse(arg: arg);

            
            