// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<Int8List>  exampleBasicListTypeI8TwinNormal({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeI8TwinNormal(arg: arg);

Future<Int16List>  exampleBasicListTypeI16TwinNormal({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeI16TwinNormal(arg: arg);

Future<Int32List>  exampleBasicListTypeI32TwinNormal({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeI32TwinNormal(arg: arg);

Future<Int64List>  exampleBasicListTypeI64TwinNormal({required Int64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeI64TwinNormal(arg: arg);

Future<Uint8List>  exampleBasicListTypeU8TwinNormal({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeU8TwinNormal(arg: arg);

Future<Uint16List>  exampleBasicListTypeU16TwinNormal({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeU16TwinNormal(arg: arg);

Future<Uint32List>  exampleBasicListTypeU32TwinNormal({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeU32TwinNormal(arg: arg);

Future<Uint64List>  exampleBasicListTypeU64TwinNormal({required Uint64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeU64TwinNormal(arg: arg);

Future<Float32List>  exampleBasicListTypeF32TwinNormal({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeF32TwinNormal(arg: arg);

Future<Float64List>  exampleBasicListTypeF64TwinNormal({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeF64TwinNormal(arg: arg);

Future<List<bool>>  exampleBasicListTypeBoolTwinNormal({required List<bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeBoolTwinNormal(arg: arg);

Future<List<String>>  exampleBasicListTypeStringTwinNormal({required List<String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeStringTwinNormal(arg: arg);

Future<List<Uint8List>>  exampleBasicListTypeBytesTwinNormal({required List<Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeBytesTwinNormal(arg: arg);

Future<List<BasicPrimitiveEnumTwinNormal>>  exampleBasicListTypeBasicPrimitiveEnumTwinNormalTwinNormal({required List<BasicPrimitiveEnumTwinNormal> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeBasicPrimitiveEnumTwinNormalTwinNormal(arg: arg);

Future<List<BasicGeneralEnumTwinNormal>>  exampleBasicListTypeBasicGeneralEnumTwinNormalTwinNormal({required List<BasicGeneralEnumTwinNormal> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeBasicGeneralEnumTwinNormalTwinNormal(arg: arg);

Future<List<BasicStructTwinNormal>>  exampleBasicListTypeBasicStructTwinNormalTwinNormal({required List<BasicStructTwinNormal> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListExampleBasicListTypeBasicStructTwinNormalTwinNormal(arg: arg);

            
            