// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../auxiliary/sample_types.dart';
import '../../frb_generated.dart';
import '../mirror.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<ApplicationSettings>  getAppSettingsTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseGetAppSettingsTwinRustAsyncSse();

Future<ApplicationSettings>  getFallibleAppSettingsTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseGetFallibleAppSettingsTwinRustAsyncSse();

Future<bool>  isAppEmbeddedTwinRustAsyncSse({required ApplicationSettings appSettings }) => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseIsAppEmbeddedTwinRustAsyncSse(appSettings: appSettings);

Stream<ApplicationSettings>  appSettingsStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseAppSettingsStreamTwinRustAsyncSse();

Stream<List<ApplicationSettings>>  appSettingsVecStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseAppSettingsVecStreamTwinRustAsyncSse();

Stream<MirrorStructTwinRustAsyncSse>  mirrorStructStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorStructStreamTwinRustAsyncSse();

Stream<(ApplicationSettings,RawStringEnumMirrored)>  mirrorTupleStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorTupleStreamTwinRustAsyncSse();

Future<ApplicationMessage>  getMessageTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseGetMessageTwinRustAsyncSse();

Future<Numbers>  repeatNumberTwinRustAsyncSse({required int num , required BigInt times }) => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseRepeatNumberTwinRustAsyncSse(num: num, times: times);

Future<Sequences>  repeatSequenceTwinRustAsyncSse({required int seq , required BigInt times }) => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseRepeatSequenceTwinRustAsyncSse(seq: seq, times: times);

Future<int?>  firstNumberTwinRustAsyncSse({required Numbers nums }) => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseFirstNumberTwinRustAsyncSse(nums: nums);

Future<int?>  firstSequenceTwinRustAsyncSse({required Sequences seqs }) => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseFirstSequenceTwinRustAsyncSse(seqs: seqs);

Future<RawStringMirrored>  testRawStringMirroredTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestRawStringMirroredTwinRustAsyncSse();

Future<NestedRawStringMirrored>  testNestedRawStringMirroredTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestNestedRawStringMirroredTwinRustAsyncSse();

Future<RawStringEnumMirrored>  testRawStringEnumMirroredTwinRustAsyncSse({required bool nested }) => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestRawStringEnumMirroredTwinRustAsyncSse(nested: nested);

Future<ListOfNestedRawStringMirrored>  testListOfRawNestedStringMirroredTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestListOfRawNestedStringMirroredTwinRustAsyncSse();

Future<List<RawStringMirrored>>  testFallibleOfRawStringMirroredTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestFallibleOfRawStringMirroredTwinRustAsyncSse();

Future<List<RawStringEnumMirrored>>  testListOfNestedEnumsMirroredTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestListOfNestedEnumsMirroredTwinRustAsyncSse();

Future<ContainsMirroredSubStructTwinRustAsyncSse>  testContainsMirroredSubStructTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestContainsMirroredSubStructTwinRustAsyncSse();

Future<StructWithHashMap>  testHashmapWithMirroredValueTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseTestHashmapWithMirroredValueTwinRustAsyncSse();

Stream<ApplicationMode>  mirrorEnumStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorEnumStreamTwinRustAsyncSse();

Stream<ApplicationMode?>  mirrorOptionEnumStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorOptionEnumStreamTwinRustAsyncSse();

Stream<List<ApplicationMode>>  mirrorVecEnumStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorVecEnumStreamTwinRustAsyncSse();

Stream<Map<int, ApplicationMode>>  mirrorMapEnumStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorMapEnumStreamTwinRustAsyncSse();

Stream<Set<ApplicationMode>>  mirrorSetEnumStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorSetEnumStreamTwinRustAsyncSse();

Stream<ApplicationModeArray2>  mirrorArrayEnumStreamTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualMirrorTwinRustAsyncSseMirrorArrayEnumStreamTwinRustAsyncSse();

            class AnotherTwinRustAsyncSse  {
                final String a;

                const AnotherTwinRustAsyncSse({required this.a ,});

                
                

                
        @override
        int get hashCode => a.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is AnotherTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && a == other.a;
        
            }

class ContainsMirroredSubStructTwinRustAsyncSse  {
                final RawStringMirrored test;
final AnotherTwinRustAsyncSse test2;

                const ContainsMirroredSubStructTwinRustAsyncSse({required this.test ,required this.test2 ,});

                
                

                
        @override
        int get hashCode => test.hashCode^test2.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is ContainsMirroredSubStructTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && test == other.test&& test2 == other.test2;
        
            }

class MirrorStructTwinRustAsyncSse  {
                final ApplicationSettings a;
final MyStruct b;
final List<MyEnum> c;
final List<ApplicationSettings> d;

                const MirrorStructTwinRustAsyncSse({required this.a ,required this.b ,required this.c ,required this.d ,});

                
                

                
        @override
        int get hashCode => a.hashCode^b.hashCode^c.hashCode^d.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is MirrorStructTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && a == other.a&& b == other.b&& c == other.c&& d == other.d;
        
            }
            