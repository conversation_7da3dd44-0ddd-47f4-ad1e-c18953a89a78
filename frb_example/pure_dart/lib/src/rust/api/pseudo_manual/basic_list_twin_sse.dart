// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<Int8List>  exampleBasicListTypeI8TwinSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeI8TwinSse(arg: arg);

Future<Int16List>  exampleBasicListTypeI16TwinSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeI16TwinSse(arg: arg);

Future<Int32List>  exampleBasicListTypeI32TwinSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeI32TwinSse(arg: arg);

Future<Int64List>  exampleBasicListTypeI64TwinSse({required Int64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeI64TwinSse(arg: arg);

Future<Uint8List>  exampleBasicListTypeU8TwinSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeU8TwinSse(arg: arg);

Future<Uint16List>  exampleBasicListTypeU16TwinSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeU16TwinSse(arg: arg);

Future<Uint32List>  exampleBasicListTypeU32TwinSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeU32TwinSse(arg: arg);

Future<Uint64List>  exampleBasicListTypeU64TwinSse({required Uint64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeU64TwinSse(arg: arg);

Future<Float32List>  exampleBasicListTypeF32TwinSse({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeF32TwinSse(arg: arg);

Future<Float64List>  exampleBasicListTypeF64TwinSse({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeF64TwinSse(arg: arg);

Future<List<bool>>  exampleBasicListTypeBoolTwinSse({required List<bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeBoolTwinSse(arg: arg);

Future<List<String>>  exampleBasicListTypeStringTwinSse({required List<String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeStringTwinSse(arg: arg);

Future<List<Uint8List>>  exampleBasicListTypeBytesTwinSse({required List<Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeBytesTwinSse(arg: arg);

Future<List<BasicPrimitiveEnumTwinSse>>  exampleBasicListTypeBasicPrimitiveEnumTwinSseTwinSse({required List<BasicPrimitiveEnumTwinSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeBasicPrimitiveEnumTwinSseTwinSse(arg: arg);

Future<List<BasicGeneralEnumTwinSse>>  exampleBasicListTypeBasicGeneralEnumTwinSseTwinSse({required List<BasicGeneralEnumTwinSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeBasicGeneralEnumTwinSseTwinSse(arg: arg);

Future<List<BasicStructTwinSse>>  exampleBasicListTypeBasicStructTwinSseTwinSse({required List<BasicStructTwinSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinSseExampleBasicListTypeBasicStructTwinSseTwinSse(arg: arg);

            
            