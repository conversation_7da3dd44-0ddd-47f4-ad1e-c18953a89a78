// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_rust_async_moi.freezed.dart';

            

            Future<void>  rustAutoOpaqueArgOwnTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueArgOwnTwinRustAsyncMoi(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgBorrowTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueArgBorrowTwinRustAsyncMoi(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgMutBorrowTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueArgMutBorrowTwinRustAsyncMoi(arg: arg, expect: expect, adder: adder);

Future<NonCloneSimpleTwinRustAsyncMoi>  rustAutoOpaqueReturnOwnTwinRustAsyncMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueReturnOwnTwinRustAsyncMoi(initial: initial);

Future<NonCloneSimpleTwinRustAsyncMoi>  rustAutoOpaqueArgOwnAndReturnOwnTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueArgOwnAndReturnOwnTwinRustAsyncMoi(arg: arg);

Future<void>  rustAutoOpaqueTwoArgsTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi a , required NonCloneSimpleTwinRustAsyncMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueTwoArgsTwinRustAsyncMoi(a: a, b: b);

Future<void>  rustAutoOpaqueNormalAndOpaqueArgTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueNormalAndOpaqueArgTwinRustAsyncMoi(a: a, b: b);

/// "+" inside the type signature
Future<void>  rustAutoOpaquePlusSignArgTwinRustAsyncMoi({required BoxMyTraitTwinRustAsyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaquePlusSignArgTwinRustAsyncMoi(arg: arg);

Future<BoxMyTraitTwinRustAsyncMoi>  rustAutoOpaquePlusSignReturnTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaquePlusSignReturnTwinRustAsyncMoi();

Future<void>  rustAutoOpaqueCallableArgTwinRustAsyncMoi({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueCallableArgTwinRustAsyncMoi(arg: arg);

Future<BoxFnStringString>  rustAutoOpaqueCallableReturnTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueCallableReturnTwinRustAsyncMoi();

Future<void>  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsyncMoi({required StructWithGoodAndOpaqueFieldTwinRustAsyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsyncMoi(arg: arg);

Future<StructWithGoodAndOpaqueFieldTwinRustAsyncMoi>  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsyncMoi();

Future<void>  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsyncMoi({required EnumWithGoodAndOpaqueTwinRustAsyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsyncMoi(arg: arg);

Future<EnumWithGoodAndOpaqueTwinRustAsyncMoi>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsyncMoi();

Future<EnumWithGoodAndOpaqueTwinRustAsyncMoi>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsyncMoi();

Future<void>  rustAutoOpaqueDummyTwinRustAsyncMoi({required StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncMoi a , required EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueDummyTwinRustAsyncMoi(a: a, b: b);

Future<void>  rustAutoOpaqueEnumArgBorrowTwinRustAsyncMoi({required NonCloneSimpleEnumTwinRustAsyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueEnumArgBorrowTwinRustAsyncMoi(arg: arg);

Future<NonCloneSimpleEnumTwinRustAsyncMoi>  rustAutoOpaqueEnumReturnOwnTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueEnumReturnOwnTwinRustAsyncMoi();

Stream<NonCloneSimpleTwinRustAsyncMoi>  rustAutoOpaqueStreamSinkTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueStreamSinkTwinRustAsyncMoi();

Future<void>  rustAutoOpaqueArgVecOwnTwinRustAsyncMoi({required List<NonCloneSimpleTwinRustAsyncMoi> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueArgVecOwnTwinRustAsyncMoi(arg: arg, expect: expect);

Future<List<NonCloneSimpleTwinRustAsyncMoi>>  rustAutoOpaqueReturnVecOwnTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueReturnVecOwnTwinRustAsyncMoi();

Future<void>  rustAutoOpaqueExplicitArgTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueExplicitArgTwinRustAsyncMoi(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueExplicitStructTwinRustAsyncMoi({required StructWithExplicitAutoOpaqueFieldTwinRustAsyncMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueExplicitStructTwinRustAsyncMoi(arg: arg);

Future<StructWithExplicitAutoOpaqueFieldTwinRustAsyncMoi>  rustAutoOpaqueExplicitReturnStructTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueExplicitReturnStructTwinRustAsyncMoi();

Future<NonCloneSimpleTwinRustAsyncMoi>  rustAutoOpaqueExplicitReturnTwinRustAsyncMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueExplicitReturnTwinRustAsyncMoi(initial: initial);

Future<int>  rustAutoOpaqueSleepTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi apple , required NonCloneSimpleTwinRustAsyncMoi orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueSleepTwinRustAsyncMoi(apple: apple, orange: orange);

Future<(OpaqueOneTwinRustAsyncMoi,OpaqueTwoTwinRustAsyncMoi)>  rustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsyncMoi();

Future<OpaqueTwoTwinRustAsyncMoi>  rustAutoOpaqueReturnOpaqueTwoTwinRustAsyncMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueReturnOpaqueTwoTwinRustAsyncMoi();

Future<int>  rustAutoOpaqueBorrowAndMutBorrowTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi borrow , required NonCloneSimpleTwinRustAsyncMoi mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueBorrowAndMutBorrowTwinRustAsyncMoi(borrow: borrow, mutBorrow: mutBorrow);

Future<int>  rustAutoOpaqueBorrowAndBorrowTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi a , required NonCloneSimpleTwinRustAsyncMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiRustAutoOpaqueBorrowAndBorrowTwinRustAsyncMoi(a: a, b: b);

            
                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinRustAsyncMoi + Send + Sync >>>
                abstract class BoxMyTraitTwinRustAsyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncMoi>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinRustAsyncMoi>>
                abstract class NonCloneSimpleEnumTwinRustAsyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinRustAsyncMoi>>
                abstract class NonCloneSimpleTwinRustAsyncMoi implements RustOpaqueInterface {
                     Future<void>  instanceMethodArgBorrowTwinRustAsyncMoi();


 Future<void>  instanceMethodArgMutBorrowTwinRustAsyncMoi();


 Future<void>  instanceMethodArgOwnTwinRustAsyncMoi();


 Future<int> get instanceMethodGetterTwinRustAsyncMoi;


 Future<NonCloneSimpleTwinRustAsyncMoi>  instanceMethodReturnOwnTwinRustAsyncMoi();


/// named constructor
static Future<NonCloneSimpleTwinRustAsyncMoi>  newCustomNameTwinRustAsyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiNewCustomNameTwinRustAsyncMoi();


/// unnamed constructor
static Future<NonCloneSimpleTwinRustAsyncMoi>  newTwinRustAsyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiNewTwinRustAsyncMoi();


/// constructor with Result
static Future<NonCloneSimpleTwinRustAsyncMoi>  newWithResultTwinRustAsyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiNewWithResultTwinRustAsyncMoi();


static Future<void>  staticMethodArgBorrowTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiStaticMethodArgBorrowTwinRustAsyncMoi(arg: arg);


static Future<void>  staticMethodArgMutBorrowTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiStaticMethodArgMutBorrowTwinRustAsyncMoi(arg: arg);


static Future<void>  staticMethodArgOwnTwinRustAsyncMoi({required NonCloneSimpleTwinRustAsyncMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiStaticMethodArgOwnTwinRustAsyncMoi(arg: arg);


static Future<NonCloneSimpleTwinRustAsyncMoi>  staticMethodReturnOwnTwinRustAsyncMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncMoiNonCloneSimpleTwinRustAsyncMoiStaticMethodReturnOwnTwinRustAsyncMoi();



                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinRustAsyncMoi>>
                abstract class OpaqueOneTwinRustAsyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinRustAsyncMoi>>
                abstract class OpaqueTwoTwinRustAsyncMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncMoi>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncMoi implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinRustAsyncMoi {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinRustAsyncMoi with _$EnumWithGoodAndOpaqueTwinRustAsyncMoi  {
                    const EnumWithGoodAndOpaqueTwinRustAsyncMoi._();

                     const factory EnumWithGoodAndOpaqueTwinRustAsyncMoi.good(  String field0,) = EnumWithGoodAndOpaqueTwinRustAsyncMoi_Good;
 const factory EnumWithGoodAndOpaqueTwinRustAsyncMoi.opaque(  NonCloneSimpleTwinRustAsyncMoi field0,) = EnumWithGoodAndOpaqueTwinRustAsyncMoi_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinRustAsyncMoi  {
                final NonCloneSimpleTwinRustAsyncMoi autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinRustAsyncMoi({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinRustAsyncMoi &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinRustAsyncMoi  {
                final String good;
final NonCloneSimpleTwinRustAsyncMoi opaque;
final NonCloneSimpleTwinRustAsyncMoi? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinRustAsyncMoi({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinRustAsyncMoi &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            