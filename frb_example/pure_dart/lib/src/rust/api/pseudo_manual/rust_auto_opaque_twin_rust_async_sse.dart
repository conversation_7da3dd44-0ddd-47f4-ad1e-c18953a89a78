// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_rust_async_sse.freezed.dart';

            

            Future<void>  rustAutoOpaqueArgOwnTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueArgOwnTwinRustAsyncSse(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgBorrowTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueArgBorrowTwinRustAsyncSse(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgMutBorrowTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueArgMutBorrowTwinRustAsyncSse(arg: arg, expect: expect, adder: adder);

Future<NonCloneSimpleTwinRustAsyncSse>  rustAutoOpaqueReturnOwnTwinRustAsyncSse({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueReturnOwnTwinRustAsyncSse(initial: initial);

Future<NonCloneSimpleTwinRustAsyncSse>  rustAutoOpaqueArgOwnAndReturnOwnTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueArgOwnAndReturnOwnTwinRustAsyncSse(arg: arg);

Future<void>  rustAutoOpaqueTwoArgsTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse a , required NonCloneSimpleTwinRustAsyncSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueTwoArgsTwinRustAsyncSse(a: a, b: b);

Future<void>  rustAutoOpaqueNormalAndOpaqueArgTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueNormalAndOpaqueArgTwinRustAsyncSse(a: a, b: b);

/// "+" inside the type signature
Future<void>  rustAutoOpaquePlusSignArgTwinRustAsyncSse({required BoxMyTraitTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaquePlusSignArgTwinRustAsyncSse(arg: arg);

Future<BoxMyTraitTwinRustAsyncSse>  rustAutoOpaquePlusSignReturnTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaquePlusSignReturnTwinRustAsyncSse();

Future<void>  rustAutoOpaqueCallableArgTwinRustAsyncSse({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueCallableArgTwinRustAsyncSse(arg: arg);

Future<BoxFnStringString>  rustAutoOpaqueCallableReturnTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueCallableReturnTwinRustAsyncSse();

Future<void>  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsyncSse({required StructWithGoodAndOpaqueFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsyncSse(arg: arg);

Future<StructWithGoodAndOpaqueFieldTwinRustAsyncSse>  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsyncSse();

Future<void>  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsyncSse({required EnumWithGoodAndOpaqueTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsyncSse(arg: arg);

Future<EnumWithGoodAndOpaqueTwinRustAsyncSse>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsyncSse();

Future<EnumWithGoodAndOpaqueTwinRustAsyncSse>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsyncSse();

Future<void>  rustAutoOpaqueDummyTwinRustAsyncSse({required StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncSse a , required EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueDummyTwinRustAsyncSse(a: a, b: b);

Future<void>  rustAutoOpaqueEnumArgBorrowTwinRustAsyncSse({required NonCloneSimpleEnumTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueEnumArgBorrowTwinRustAsyncSse(arg: arg);

Future<NonCloneSimpleEnumTwinRustAsyncSse>  rustAutoOpaqueEnumReturnOwnTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueEnumReturnOwnTwinRustAsyncSse();

Stream<NonCloneSimpleTwinRustAsyncSse>  rustAutoOpaqueStreamSinkTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueStreamSinkTwinRustAsyncSse();

Future<void>  rustAutoOpaqueArgVecOwnTwinRustAsyncSse({required List<NonCloneSimpleTwinRustAsyncSse> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueArgVecOwnTwinRustAsyncSse(arg: arg, expect: expect);

Future<List<NonCloneSimpleTwinRustAsyncSse>>  rustAutoOpaqueReturnVecOwnTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueReturnVecOwnTwinRustAsyncSse();

Future<void>  rustAutoOpaqueExplicitArgTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueExplicitArgTwinRustAsyncSse(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueExplicitStructTwinRustAsyncSse({required StructWithExplicitAutoOpaqueFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueExplicitStructTwinRustAsyncSse(arg: arg);

Future<StructWithExplicitAutoOpaqueFieldTwinRustAsyncSse>  rustAutoOpaqueExplicitReturnStructTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueExplicitReturnStructTwinRustAsyncSse();

Future<NonCloneSimpleTwinRustAsyncSse>  rustAutoOpaqueExplicitReturnTwinRustAsyncSse({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueExplicitReturnTwinRustAsyncSse(initial: initial);

Future<int>  rustAutoOpaqueSleepTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse apple , required NonCloneSimpleTwinRustAsyncSse orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueSleepTwinRustAsyncSse(apple: apple, orange: orange);

Future<(OpaqueOneTwinRustAsyncSse,OpaqueTwoTwinRustAsyncSse)>  rustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsyncSse();

Future<OpaqueTwoTwinRustAsyncSse>  rustAutoOpaqueReturnOpaqueTwoTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueReturnOpaqueTwoTwinRustAsyncSse();

Future<int>  rustAutoOpaqueBorrowAndMutBorrowTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse borrow , required NonCloneSimpleTwinRustAsyncSse mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueBorrowAndMutBorrowTwinRustAsyncSse(borrow: borrow, mutBorrow: mutBorrow);

Future<int>  rustAutoOpaqueBorrowAndBorrowTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse a , required NonCloneSimpleTwinRustAsyncSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseRustAutoOpaqueBorrowAndBorrowTwinRustAsyncSse(a: a, b: b);

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinRustAsyncSse + Send + Sync >>>
                abstract class BoxMyTraitTwinRustAsyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncSse>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinRustAsyncSse>>
                abstract class NonCloneSimpleEnumTwinRustAsyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinRustAsyncSse>>
                abstract class NonCloneSimpleTwinRustAsyncSse implements RustOpaqueInterface {
                     Future<void>  instanceMethodArgBorrowTwinRustAsyncSse();


 Future<void>  instanceMethodArgMutBorrowTwinRustAsyncSse();


 Future<void>  instanceMethodArgOwnTwinRustAsyncSse();


 Future<int> get instanceMethodGetterTwinRustAsyncSse;


 Future<NonCloneSimpleTwinRustAsyncSse>  instanceMethodReturnOwnTwinRustAsyncSse();


/// named constructor
static Future<NonCloneSimpleTwinRustAsyncSse>  newCustomNameTwinRustAsyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseNewCustomNameTwinRustAsyncSse();


/// unnamed constructor
static Future<NonCloneSimpleTwinRustAsyncSse>  newTwinRustAsyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseNewTwinRustAsyncSse();


/// constructor with Result
static Future<NonCloneSimpleTwinRustAsyncSse>  newWithResultTwinRustAsyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseNewWithResultTwinRustAsyncSse();


static Future<void>  staticMethodArgBorrowTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseStaticMethodArgBorrowTwinRustAsyncSse(arg: arg);


static Future<void>  staticMethodArgMutBorrowTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseStaticMethodArgMutBorrowTwinRustAsyncSse(arg: arg);


static Future<void>  staticMethodArgOwnTwinRustAsyncSse({required NonCloneSimpleTwinRustAsyncSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseStaticMethodArgOwnTwinRustAsyncSse(arg: arg);


static Future<NonCloneSimpleTwinRustAsyncSse>  staticMethodReturnOwnTwinRustAsyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseNonCloneSimpleTwinRustAsyncSseStaticMethodReturnOwnTwinRustAsyncSse();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinRustAsyncSse>>
                abstract class OpaqueOneTwinRustAsyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinRustAsyncSse>>
                abstract class OpaqueTwoTwinRustAsyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncSse>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncSse implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinRustAsyncSse {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinRustAsyncSse with _$EnumWithGoodAndOpaqueTwinRustAsyncSse  {
                    const EnumWithGoodAndOpaqueTwinRustAsyncSse._();

                     const factory EnumWithGoodAndOpaqueTwinRustAsyncSse.good(  String field0,) = EnumWithGoodAndOpaqueTwinRustAsyncSse_Good;
 const factory EnumWithGoodAndOpaqueTwinRustAsyncSse.opaque(  NonCloneSimpleTwinRustAsyncSse field0,) = EnumWithGoodAndOpaqueTwinRustAsyncSse_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinRustAsyncSse  {
                final NonCloneSimpleTwinRustAsyncSse autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinRustAsyncSse({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinRustAsyncSse  {
                final String good;
final NonCloneSimpleTwinRustAsyncSse opaque;
final NonCloneSimpleTwinRustAsyncSse? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinRustAsyncSse({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            