// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_sse.freezed.dart';

            

            Future<void>  rustAutoOpaqueArgOwnTwinSse({required NonCloneSimpleTwinSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueArgOwnTwinSse(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgBorrowTwinSse({required NonCloneSimpleTwinSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueArgBorrowTwinSse(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgMutBorrowTwinSse({required NonCloneSimpleTwinSse arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueArgMutBorrowTwinSse(arg: arg, expect: expect, adder: adder);

Future<NonCloneSimpleTwinSse>  rustAutoOpaqueReturnOwnTwinSse({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueReturnOwnTwinSse(initial: initial);

Future<NonCloneSimpleTwinSse>  rustAutoOpaqueArgOwnAndReturnOwnTwinSse({required NonCloneSimpleTwinSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueArgOwnAndReturnOwnTwinSse(arg: arg);

Future<void>  rustAutoOpaqueTwoArgsTwinSse({required NonCloneSimpleTwinSse a , required NonCloneSimpleTwinSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueTwoArgsTwinSse(a: a, b: b);

Future<void>  rustAutoOpaqueNormalAndOpaqueArgTwinSse({required NonCloneSimpleTwinSse a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueNormalAndOpaqueArgTwinSse(a: a, b: b);

/// "+" inside the type signature
Future<void>  rustAutoOpaquePlusSignArgTwinSse({required BoxMyTraitTwinSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaquePlusSignArgTwinSse(arg: arg);

Future<BoxMyTraitTwinSse>  rustAutoOpaquePlusSignReturnTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaquePlusSignReturnTwinSse();

Future<void>  rustAutoOpaqueCallableArgTwinSse({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueCallableArgTwinSse(arg: arg);

Future<BoxFnStringString>  rustAutoOpaqueCallableReturnTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueCallableReturnTwinSse();

Future<void>  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSse({required StructWithGoodAndOpaqueFieldTwinSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSse(arg: arg);

Future<StructWithGoodAndOpaqueFieldTwinSse>  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSse();

Future<void>  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSse({required EnumWithGoodAndOpaqueTwinSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSse(arg: arg);

Future<EnumWithGoodAndOpaqueTwinSse>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSse();

Future<EnumWithGoodAndOpaqueTwinSse>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSse();

Future<void>  rustAutoOpaqueDummyTwinSse({required StructWithGoodAndOpaqueFieldWithoutOptionTwinSse a , required EnumWithGoodAndOpaqueWithoutOptionTwinSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueDummyTwinSse(a: a, b: b);

Future<void>  rustAutoOpaqueEnumArgBorrowTwinSse({required NonCloneSimpleEnumTwinSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueEnumArgBorrowTwinSse(arg: arg);

Future<NonCloneSimpleEnumTwinSse>  rustAutoOpaqueEnumReturnOwnTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueEnumReturnOwnTwinSse();

Stream<NonCloneSimpleTwinSse>  rustAutoOpaqueStreamSinkTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueStreamSinkTwinSse();

Future<void>  rustAutoOpaqueArgVecOwnTwinSse({required List<NonCloneSimpleTwinSse> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueArgVecOwnTwinSse(arg: arg, expect: expect);

Future<List<NonCloneSimpleTwinSse>>  rustAutoOpaqueReturnVecOwnTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueReturnVecOwnTwinSse();

Future<void>  rustAutoOpaqueExplicitArgTwinSse({required NonCloneSimpleTwinSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueExplicitArgTwinSse(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueExplicitStructTwinSse({required StructWithExplicitAutoOpaqueFieldTwinSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueExplicitStructTwinSse(arg: arg);

Future<StructWithExplicitAutoOpaqueFieldTwinSse>  rustAutoOpaqueExplicitReturnStructTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueExplicitReturnStructTwinSse();

Future<NonCloneSimpleTwinSse>  rustAutoOpaqueExplicitReturnTwinSse({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueExplicitReturnTwinSse(initial: initial);

Future<int>  rustAutoOpaqueSleepTwinSse({required NonCloneSimpleTwinSse apple , required NonCloneSimpleTwinSse orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueSleepTwinSse(apple: apple, orange: orange);

Future<(OpaqueOneTwinSse,OpaqueTwoTwinSse)>  rustAutoOpaqueReturnOpaqueOneAndTwoTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueReturnOpaqueOneAndTwoTwinSse();

Future<OpaqueTwoTwinSse>  rustAutoOpaqueReturnOpaqueTwoTwinSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueReturnOpaqueTwoTwinSse();

Future<int>  rustAutoOpaqueBorrowAndMutBorrowTwinSse({required NonCloneSimpleTwinSse borrow , required NonCloneSimpleTwinSse mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueBorrowAndMutBorrowTwinSse(borrow: borrow, mutBorrow: mutBorrow);

Future<int>  rustAutoOpaqueBorrowAndBorrowTwinSse({required NonCloneSimpleTwinSse a , required NonCloneSimpleTwinSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseRustAutoOpaqueBorrowAndBorrowTwinSse(a: a, b: b);

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinSse + Send + Sync >>>
                abstract class BoxMyTraitTwinSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinSse>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinSse>>
                abstract class NonCloneSimpleEnumTwinSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinSse>>
                abstract class NonCloneSimpleTwinSse implements RustOpaqueInterface {
                     Future<void>  instanceMethodArgBorrowTwinSse();


 Future<void>  instanceMethodArgMutBorrowTwinSse();


 Future<void>  instanceMethodArgOwnTwinSse();


 Future<int> get instanceMethodGetterTwinSse;


 Future<NonCloneSimpleTwinSse>  instanceMethodReturnOwnTwinSse();


/// named constructor
static Future<NonCloneSimpleTwinSse>  newCustomNameTwinSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseNewCustomNameTwinSse();


/// unnamed constructor
static Future<NonCloneSimpleTwinSse>  newTwinSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseNewTwinSse();


/// constructor with Result
static Future<NonCloneSimpleTwinSse>  newWithResultTwinSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseNewWithResultTwinSse();


static Future<void>  staticMethodArgBorrowTwinSse({required NonCloneSimpleTwinSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseStaticMethodArgBorrowTwinSse(arg: arg);


static Future<void>  staticMethodArgMutBorrowTwinSse({required NonCloneSimpleTwinSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseStaticMethodArgMutBorrowTwinSse(arg: arg);


static Future<void>  staticMethodArgOwnTwinSse({required NonCloneSimpleTwinSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseStaticMethodArgOwnTwinSse(arg: arg);


static Future<NonCloneSimpleTwinSse>  staticMethodReturnOwnTwinSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSseNonCloneSimpleTwinSseStaticMethodReturnOwnTwinSse();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinSse>>
                abstract class OpaqueOneTwinSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinSse>>
                abstract class OpaqueTwoTwinSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinSse>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinSse implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinSse {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinSse with _$EnumWithGoodAndOpaqueTwinSse  {
                    const EnumWithGoodAndOpaqueTwinSse._();

                     const factory EnumWithGoodAndOpaqueTwinSse.good(  String field0,) = EnumWithGoodAndOpaqueTwinSse_Good;
 const factory EnumWithGoodAndOpaqueTwinSse.opaque(  NonCloneSimpleTwinSse field0,) = EnumWithGoodAndOpaqueTwinSse_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinSse  {
                final NonCloneSimpleTwinSse autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinSse({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinSse &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinSse  {
                final String good;
final NonCloneSimpleTwinSse opaque;
final NonCloneSimpleTwinSse? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinSse({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinSse &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            