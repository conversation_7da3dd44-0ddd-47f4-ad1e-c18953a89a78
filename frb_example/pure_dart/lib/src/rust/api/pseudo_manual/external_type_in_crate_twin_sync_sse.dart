// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../auxiliary/new_module_system/sub_module.dart';
import '../../auxiliary/old_module_system/sub_module.dart';
import '../../auxiliary/sample_types.dart';
import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            bool  useImportedStructTwinSyncSse({required MyStruct myStruct }) => RustLib.instance.api.crateApiPseudoManualExternalTypeInCrateTwinSyncSseUseImportedStructTwinSyncSse(myStruct: myStruct);

bool  useImportedEnumTwinSyncSse({required MyEnum myEnum }) => RustLib.instance.api.crateApiPseudoManualExternalTypeInCrateTwinSyncSseUseImportedEnumTwinSyncSse(myEnum: myEnum);

OldSimpleStruct  callOldModuleSystemTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualExternalTypeInCrateTwinSyncSseCallOldModuleSystemTwinSyncSse();

NewSimpleStruct  callNewModuleSystemTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualExternalTypeInCrateTwinSyncSseCallNewModuleSystemTwinSyncSse();

            
            