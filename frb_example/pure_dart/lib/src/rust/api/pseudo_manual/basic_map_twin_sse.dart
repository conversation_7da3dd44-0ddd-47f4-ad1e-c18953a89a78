// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<Map<int, int>>  exampleBasicMapTypeI8TwinSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeI8TwinSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeI16TwinSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeI16TwinSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeI32TwinSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeI32TwinSse(arg: arg);

Future<Map<int, PlatformInt64>>  exampleBasicMapTypeI64TwinSse({required Map<int, PlatformInt64> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeI64TwinSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeI128TwinSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeI128TwinSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeU8TwinSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeU8TwinSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeU16TwinSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeU16TwinSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeU32TwinSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeU32TwinSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeU64TwinSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeU64TwinSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeU128TwinSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeU128TwinSse(arg: arg);

Future<Map<int, PlatformInt64>>  exampleBasicMapTypeIsizeTwinSse({required Map<int, PlatformInt64> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeIsizeTwinSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeUsizeTwinSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeUsizeTwinSse(arg: arg);

Future<Map<int, double>>  exampleBasicMapTypeF32TwinSse({required Map<int, double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeF32TwinSse(arg: arg);

Future<Map<int, double>>  exampleBasicMapTypeF64TwinSse({required Map<int, double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeF64TwinSse(arg: arg);

Future<Map<int, bool>>  exampleBasicMapTypeBoolTwinSse({required Map<int, bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeBoolTwinSse(arg: arg);

Future<Map<int, String>>  exampleBasicMapTypeStringTwinSse({required Map<int, String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeStringTwinSse(arg: arg);

Future<Map<int, Uint8List>>  exampleBasicMapTypeBytesTwinSse({required Map<int, Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeBytesTwinSse(arg: arg);

Future<Map<int, BasicPrimitiveEnumTwinSse>>  exampleBasicMapTypeBasicPrimitiveEnumTwinSseTwinSse({required Map<int, BasicPrimitiveEnumTwinSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeBasicPrimitiveEnumTwinSseTwinSse(arg: arg);

Future<Map<int, BasicGeneralEnumTwinSse>>  exampleBasicMapTypeBasicGeneralEnumTwinSseTwinSse({required Map<int, BasicGeneralEnumTwinSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeBasicGeneralEnumTwinSseTwinSse(arg: arg);

Future<Map<int, BasicStructTwinSse>>  exampleBasicMapTypeBasicStructTwinSseTwinSse({required Map<int, BasicStructTwinSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSseExampleBasicMapTypeBasicStructTwinSseTwinSse(arg: arg);

            
            