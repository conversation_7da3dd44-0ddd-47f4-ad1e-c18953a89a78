// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
part 'raw_string_twin_sse.freezed.dart';

            

            Future<RawStringItemStructTwinSse>  testRawStringItemStructTwinSse() => RustLib.instance.api.crateApiPseudoManualRawStringTwinSseTestRawStringItemStructTwinSse();

Future<RawStringItemEnumTwinSse>  testRawStringItemEnumTwinSse() => RustLib.instance.api.crateApiPseudoManualRawStringTwinSseTestRawStringItemEnumTwinSse();

Future<MoreThanJustOneRawStringStructTwinSse>  testMoreThanJustOneRawStringStructTwinSse() => RustLib.instance.api.crateApiPseudoManualRawStringTwinSseTestMoreThanJustOneRawStringStructTwinSse();

            class MoreThanJustOneRawStringStructTwinSse  {
                final String regular;
final String type;
final bool async_;
final String another;

                const MoreThanJustOneRawStringStructTwinSse({required this.regular ,required this.type ,required this.async_ ,required this.another ,});

                
                

                
        @override
        int get hashCode => regular.hashCode^type.hashCode^async_.hashCode^another.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is MoreThanJustOneRawStringStructTwinSse &&
                runtimeType == other.runtimeType
                && regular == other.regular&& type == other.type&& async_ == other.async_&& another == other.another;
        
            }

@freezed
                sealed class RawStringItemEnumTwinSse with _$RawStringItemEnumTwinSse  {
                    const RawStringItemEnumTwinSse._();

                     const factory RawStringItemEnumTwinSse.regular({   required String regular , }) = RawStringItemEnumTwinSse_Regular;
 const factory RawStringItemEnumTwinSse.raw({   required String type , }) = RawStringItemEnumTwinSse_Raw;

                    

                    
                }

class RawStringItemStructTwinSse  {
                final String type;

                const RawStringItemStructTwinSse({required this.type ,});

                
                

                
        @override
        int get hashCode => type.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is RawStringItemStructTwinSse &&
                runtimeType == other.runtimeType
                && type == other.type;
        
            }
            