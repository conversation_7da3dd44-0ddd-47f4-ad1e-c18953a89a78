// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_sync.freezed.dart';

            

            void  rustAutoOpaqueArgOwnTwinSync({required NonCloneSimpleTwinSync arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueArgOwnTwinSync(arg: arg, expect: expect);

void  rustAutoOpaqueArgBorrowTwinSync({required NonCloneSimpleTwinSync arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueArgBorrowTwinSync(arg: arg, expect: expect);

void  rustAutoOpaqueArgMutBorrowTwinSync({required NonCloneSimpleTwinSync arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueArgMutBorrowTwinSync(arg: arg, expect: expect, adder: adder);

NonCloneSimpleTwinSync  rustAutoOpaqueReturnOwnTwinSync({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueReturnOwnTwinSync(initial: initial);

NonCloneSimpleTwinSync  rustAutoOpaqueArgOwnAndReturnOwnTwinSync({required NonCloneSimpleTwinSync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueArgOwnAndReturnOwnTwinSync(arg: arg);

void  rustAutoOpaqueTwoArgsTwinSync({required NonCloneSimpleTwinSync a , required NonCloneSimpleTwinSync b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueTwoArgsTwinSync(a: a, b: b);

void  rustAutoOpaqueNormalAndOpaqueArgTwinSync({required NonCloneSimpleTwinSync a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueNormalAndOpaqueArgTwinSync(a: a, b: b);

/// "+" inside the type signature
void  rustAutoOpaquePlusSignArgTwinSync({required BoxMyTraitTwinSync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaquePlusSignArgTwinSync(arg: arg);

BoxMyTraitTwinSync  rustAutoOpaquePlusSignReturnTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaquePlusSignReturnTwinSync();

void  rustAutoOpaqueCallableArgTwinSync({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueCallableArgTwinSync(arg: arg);

BoxFnStringString  rustAutoOpaqueCallableReturnTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueCallableReturnTwinSync();

void  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSync({required StructWithGoodAndOpaqueFieldTwinSync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSync(arg: arg);

StructWithGoodAndOpaqueFieldTwinSync  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSync();

void  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSync({required EnumWithGoodAndOpaqueTwinSync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSync(arg: arg);

EnumWithGoodAndOpaqueTwinSync  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSync();

EnumWithGoodAndOpaqueTwinSync  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSync();

void  rustAutoOpaqueDummyTwinSync({required StructWithGoodAndOpaqueFieldWithoutOptionTwinSync a , required EnumWithGoodAndOpaqueWithoutOptionTwinSync b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueDummyTwinSync(a: a, b: b);

void  rustAutoOpaqueEnumArgBorrowTwinSync({required NonCloneSimpleEnumTwinSync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueEnumArgBorrowTwinSync(arg: arg);

NonCloneSimpleEnumTwinSync  rustAutoOpaqueEnumReturnOwnTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueEnumReturnOwnTwinSync();

Stream<NonCloneSimpleTwinSync>  rustAutoOpaqueStreamSinkTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueStreamSinkTwinSync();

void  rustAutoOpaqueArgVecOwnTwinSync({required List<NonCloneSimpleTwinSync> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueArgVecOwnTwinSync(arg: arg, expect: expect);

List<NonCloneSimpleTwinSync>  rustAutoOpaqueReturnVecOwnTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueReturnVecOwnTwinSync();

void  rustAutoOpaqueExplicitArgTwinSync({required NonCloneSimpleTwinSync arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueExplicitArgTwinSync(arg: arg, expect: expect);

void  rustAutoOpaqueExplicitStructTwinSync({required StructWithExplicitAutoOpaqueFieldTwinSync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueExplicitStructTwinSync(arg: arg);

StructWithExplicitAutoOpaqueFieldTwinSync  rustAutoOpaqueExplicitReturnStructTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueExplicitReturnStructTwinSync();

NonCloneSimpleTwinSync  rustAutoOpaqueExplicitReturnTwinSync({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueExplicitReturnTwinSync(initial: initial);

int  rustAutoOpaqueSleepTwinSync({required NonCloneSimpleTwinSync apple , required NonCloneSimpleTwinSync orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueSleepTwinSync(apple: apple, orange: orange);

(OpaqueOneTwinSync,OpaqueTwoTwinSync)  rustAutoOpaqueReturnOpaqueOneAndTwoTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueReturnOpaqueOneAndTwoTwinSync();

OpaqueTwoTwinSync  rustAutoOpaqueReturnOpaqueTwoTwinSync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueReturnOpaqueTwoTwinSync();

int  rustAutoOpaqueBorrowAndMutBorrowTwinSync({required NonCloneSimpleTwinSync borrow , required NonCloneSimpleTwinSync mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueBorrowAndMutBorrowTwinSync(borrow: borrow, mutBorrow: mutBorrow);

int  rustAutoOpaqueBorrowAndBorrowTwinSync({required NonCloneSimpleTwinSync a , required NonCloneSimpleTwinSync b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncRustAutoOpaqueBorrowAndBorrowTwinSync(a: a, b: b);

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinSync + Send + Sync >>>
                abstract class BoxMyTraitTwinSync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinSync>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinSync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinSync>>
                abstract class NonCloneSimpleEnumTwinSync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinSync>>
                abstract class NonCloneSimpleTwinSync implements RustOpaqueInterface {
                     void  instanceMethodArgBorrowTwinSync();


 void  instanceMethodArgMutBorrowTwinSync();


 void  instanceMethodArgOwnTwinSync();


 int get instanceMethodGetterTwinSync;


 NonCloneSimpleTwinSync  instanceMethodReturnOwnTwinSync();


/// named constructor
static NonCloneSimpleTwinSync  newCustomNameTwinSync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncNewCustomNameTwinSync();


/// unnamed constructor
static NonCloneSimpleTwinSync  newTwinSync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncNewTwinSync();


/// constructor with Result
static NonCloneSimpleTwinSync  newWithResultTwinSync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncNewWithResultTwinSync();


static void  staticMethodArgBorrowTwinSync({required NonCloneSimpleTwinSync arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncStaticMethodArgBorrowTwinSync(arg: arg);


static void  staticMethodArgMutBorrowTwinSync({required NonCloneSimpleTwinSync arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncStaticMethodArgMutBorrowTwinSync(arg: arg);


static void  staticMethodArgOwnTwinSync({required NonCloneSimpleTwinSync arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncStaticMethodArgOwnTwinSync(arg: arg);


static NonCloneSimpleTwinSync  staticMethodReturnOwnTwinSync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncNonCloneSimpleTwinSyncStaticMethodReturnOwnTwinSync();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinSync>>
                abstract class OpaqueOneTwinSync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinSync>>
                abstract class OpaqueTwoTwinSync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinSync>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinSync implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinSync {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinSync with _$EnumWithGoodAndOpaqueTwinSync  {
                    const EnumWithGoodAndOpaqueTwinSync._();

                     const factory EnumWithGoodAndOpaqueTwinSync.good(  String field0,) = EnumWithGoodAndOpaqueTwinSync_Good;
 const factory EnumWithGoodAndOpaqueTwinSync.opaque(  NonCloneSimpleTwinSync field0,) = EnumWithGoodAndOpaqueTwinSync_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinSync  {
                final NonCloneSimpleTwinSync autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinSync({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinSync &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinSync  {
                final String good;
final NonCloneSimpleTwinSync opaque;
final NonCloneSimpleTwinSync? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinSync({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinSync &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            