// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_rust_async_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<Int8List>  exampleBasicListTypeI8TwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeI8TwinRustAsyncSse(arg: arg);

Future<Int16List>  exampleBasicListTypeI16TwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeI16TwinRustAsyncSse(arg: arg);

Future<Int32List>  exampleBasicListTypeI32TwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeI32TwinRustAsyncSse(arg: arg);

Future<Int64List>  exampleBasicListTypeI64TwinRustAsyncSse({required Int64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeI64TwinRustAsyncSse(arg: arg);

Future<Uint8List>  exampleBasicListTypeU8TwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeU8TwinRustAsyncSse(arg: arg);

Future<Uint16List>  exampleBasicListTypeU16TwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeU16TwinRustAsyncSse(arg: arg);

Future<Uint32List>  exampleBasicListTypeU32TwinRustAsyncSse({required List<int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeU32TwinRustAsyncSse(arg: arg);

Future<Uint64List>  exampleBasicListTypeU64TwinRustAsyncSse({required Uint64List arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeU64TwinRustAsyncSse(arg: arg);

Future<Float32List>  exampleBasicListTypeF32TwinRustAsyncSse({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeF32TwinRustAsyncSse(arg: arg);

Future<Float64List>  exampleBasicListTypeF64TwinRustAsyncSse({required List<double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeF64TwinRustAsyncSse(arg: arg);

Future<List<bool>>  exampleBasicListTypeBoolTwinRustAsyncSse({required List<bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeBoolTwinRustAsyncSse(arg: arg);

Future<List<String>>  exampleBasicListTypeStringTwinRustAsyncSse({required List<String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeStringTwinRustAsyncSse(arg: arg);

Future<List<Uint8List>>  exampleBasicListTypeBytesTwinRustAsyncSse({required List<Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeBytesTwinRustAsyncSse(arg: arg);

Future<List<BasicPrimitiveEnumTwinRustAsyncSse>>  exampleBasicListTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse({required List<BasicPrimitiveEnumTwinRustAsyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<List<BasicGeneralEnumTwinRustAsyncSse>>  exampleBasicListTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse({required List<BasicGeneralEnumTwinRustAsyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<List<BasicStructTwinRustAsyncSse>>  exampleBasicListTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse({required List<BasicStructTwinRustAsyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicListTwinRustAsyncSseExampleBasicListTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

            
            