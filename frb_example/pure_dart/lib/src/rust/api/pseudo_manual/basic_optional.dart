// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<int?>  exampleBasicOptionalTypeI8TwinNormal({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeI8TwinNormal(arg: arg);

Future<int?>  exampleBasicOptionalTypeI16TwinNormal({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeI16TwinNormal(arg: arg);

Future<int?>  exampleBasicOptionalTypeI32TwinNormal({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeI32TwinNormal(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeI64TwinNormal({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeI64TwinNormal(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeI128TwinNormal({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeI128TwinNormal(arg: arg);

Future<int?>  exampleBasicOptionalTypeU8TwinNormal({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeU8TwinNormal(arg: arg);

Future<int?>  exampleBasicOptionalTypeU16TwinNormal({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeU16TwinNormal(arg: arg);

Future<int?>  exampleBasicOptionalTypeU32TwinNormal({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeU32TwinNormal(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU64TwinNormal({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeU64TwinNormal(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU128TwinNormal({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeU128TwinNormal(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeIsizeTwinNormal({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeIsizeTwinNormal(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeUsizeTwinNormal({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeUsizeTwinNormal(arg: arg);

Future<double?>  exampleBasicOptionalTypeF32TwinNormal({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeF32TwinNormal(arg: arg);

Future<double?>  exampleBasicOptionalTypeF64TwinNormal({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeF64TwinNormal(arg: arg);

Future<bool?>  exampleBasicOptionalTypeBoolTwinNormal({bool? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeBoolTwinNormal(arg: arg);

Future<String?>  exampleBasicOptionalTypeStringTwinNormal({String? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeStringTwinNormal(arg: arg);

Future<Uint8List?>  exampleBasicOptionalTypeBytesTwinNormal({Uint8List? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeBytesTwinNormal(arg: arg);

Future<BasicPrimitiveEnumTwinNormal?>  exampleBasicOptionalTypeBasicPrimitiveEnumTwinNormalTwinNormal({BasicPrimitiveEnumTwinNormal? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeBasicPrimitiveEnumTwinNormalTwinNormal(arg: arg);

Future<BasicGeneralEnumTwinNormal?>  exampleBasicOptionalTypeBasicGeneralEnumTwinNormalTwinNormal({BasicGeneralEnumTwinNormal? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeBasicGeneralEnumTwinNormalTwinNormal(arg: arg);

Future<BasicStructTwinNormal?>  exampleBasicOptionalTypeBasicStructTwinNormalTwinNormal({BasicStructTwinNormal? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalExampleBasicOptionalTypeBasicStructTwinNormalTwinNormal(arg: arg);

            
            