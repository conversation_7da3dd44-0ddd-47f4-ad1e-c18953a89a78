// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sync_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            int?  exampleBasicOptionalTypeI8TwinSyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeI8TwinSyncSse(arg: arg);

int?  exampleBasicOptionalTypeI16TwinSyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeI16TwinSyncSse(arg: arg);

int?  exampleBasicOptionalTypeI32TwinSyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeI32TwinSyncSse(arg: arg);

PlatformInt64?  exampleBasicOptionalTypeI64TwinSyncSse({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeI64TwinSyncSse(arg: arg);

BigInt?  exampleBasicOptionalTypeI128TwinSyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeI128TwinSyncSse(arg: arg);

int?  exampleBasicOptionalTypeU8TwinSyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeU8TwinSyncSse(arg: arg);

int?  exampleBasicOptionalTypeU16TwinSyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeU16TwinSyncSse(arg: arg);

int?  exampleBasicOptionalTypeU32TwinSyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeU32TwinSyncSse(arg: arg);

BigInt?  exampleBasicOptionalTypeU64TwinSyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeU64TwinSyncSse(arg: arg);

BigInt?  exampleBasicOptionalTypeU128TwinSyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeU128TwinSyncSse(arg: arg);

PlatformInt64?  exampleBasicOptionalTypeIsizeTwinSyncSse({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeIsizeTwinSyncSse(arg: arg);

BigInt?  exampleBasicOptionalTypeUsizeTwinSyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeUsizeTwinSyncSse(arg: arg);

double?  exampleBasicOptionalTypeF32TwinSyncSse({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeF32TwinSyncSse(arg: arg);

double?  exampleBasicOptionalTypeF64TwinSyncSse({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeF64TwinSyncSse(arg: arg);

bool?  exampleBasicOptionalTypeBoolTwinSyncSse({bool? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeBoolTwinSyncSse(arg: arg);

String?  exampleBasicOptionalTypeStringTwinSyncSse({String? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeStringTwinSyncSse(arg: arg);

Uint8List?  exampleBasicOptionalTypeBytesTwinSyncSse({Uint8List? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeBytesTwinSyncSse(arg: arg);

BasicPrimitiveEnumTwinSyncSse?  exampleBasicOptionalTypeBasicPrimitiveEnumTwinSyncSseTwinSyncSse({BasicPrimitiveEnumTwinSyncSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeBasicPrimitiveEnumTwinSyncSseTwinSyncSse(arg: arg);

BasicGeneralEnumTwinSyncSse?  exampleBasicOptionalTypeBasicGeneralEnumTwinSyncSseTwinSyncSse({BasicGeneralEnumTwinSyncSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeBasicGeneralEnumTwinSyncSseTwinSyncSse(arg: arg);

BasicStructTwinSyncSse?  exampleBasicOptionalTypeBasicStructTwinSyncSseTwinSyncSse({BasicStructTwinSyncSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSyncSseExampleBasicOptionalTypeBasicStructTwinSyncSseTwinSyncSse(arg: arg);

            
            