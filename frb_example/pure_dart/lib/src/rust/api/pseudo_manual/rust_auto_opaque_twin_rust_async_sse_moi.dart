// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_rust_async_sse_moi.freezed.dart';

            

            Future<void>  rustAutoOpaqueArgOwnTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueArgOwnTwinRustAsyncSseMoi(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgBorrowTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueArgBorrowTwinRustAsyncSseMoi(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgMutBorrowTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueArgMutBorrowTwinRustAsyncSseMoi(arg: arg, expect: expect, adder: adder);

Future<NonCloneSimpleTwinRustAsyncSseMoi>  rustAutoOpaqueReturnOwnTwinRustAsyncSseMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueReturnOwnTwinRustAsyncSseMoi(initial: initial);

Future<NonCloneSimpleTwinRustAsyncSseMoi>  rustAutoOpaqueArgOwnAndReturnOwnTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueArgOwnAndReturnOwnTwinRustAsyncSseMoi(arg: arg);

Future<void>  rustAutoOpaqueTwoArgsTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi a , required NonCloneSimpleTwinRustAsyncSseMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueTwoArgsTwinRustAsyncSseMoi(a: a, b: b);

Future<void>  rustAutoOpaqueNormalAndOpaqueArgTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueNormalAndOpaqueArgTwinRustAsyncSseMoi(a: a, b: b);

/// "+" inside the type signature
Future<void>  rustAutoOpaquePlusSignArgTwinRustAsyncSseMoi({required BoxMyTraitTwinRustAsyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaquePlusSignArgTwinRustAsyncSseMoi(arg: arg);

Future<BoxMyTraitTwinRustAsyncSseMoi>  rustAutoOpaquePlusSignReturnTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaquePlusSignReturnTwinRustAsyncSseMoi();

Future<void>  rustAutoOpaqueCallableArgTwinRustAsyncSseMoi({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueCallableArgTwinRustAsyncSseMoi(arg: arg);

Future<BoxFnStringString>  rustAutoOpaqueCallableReturnTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueCallableReturnTwinRustAsyncSseMoi();

Future<void>  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsyncSseMoi({required StructWithGoodAndOpaqueFieldTwinRustAsyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsyncSseMoi(arg: arg);

Future<StructWithGoodAndOpaqueFieldTwinRustAsyncSseMoi>  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsyncSseMoi();

Future<void>  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsyncSseMoi({required EnumWithGoodAndOpaqueTwinRustAsyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsyncSseMoi(arg: arg);

Future<EnumWithGoodAndOpaqueTwinRustAsyncSseMoi>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsyncSseMoi();

Future<EnumWithGoodAndOpaqueTwinRustAsyncSseMoi>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsyncSseMoi();

Future<void>  rustAutoOpaqueDummyTwinRustAsyncSseMoi({required StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncSseMoi a , required EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncSseMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueDummyTwinRustAsyncSseMoi(a: a, b: b);

Future<void>  rustAutoOpaqueEnumArgBorrowTwinRustAsyncSseMoi({required NonCloneSimpleEnumTwinRustAsyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueEnumArgBorrowTwinRustAsyncSseMoi(arg: arg);

Future<NonCloneSimpleEnumTwinRustAsyncSseMoi>  rustAutoOpaqueEnumReturnOwnTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueEnumReturnOwnTwinRustAsyncSseMoi();

Stream<NonCloneSimpleTwinRustAsyncSseMoi>  rustAutoOpaqueStreamSinkTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueStreamSinkTwinRustAsyncSseMoi();

Future<void>  rustAutoOpaqueArgVecOwnTwinRustAsyncSseMoi({required List<NonCloneSimpleTwinRustAsyncSseMoi> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueArgVecOwnTwinRustAsyncSseMoi(arg: arg, expect: expect);

Future<List<NonCloneSimpleTwinRustAsyncSseMoi>>  rustAutoOpaqueReturnVecOwnTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueReturnVecOwnTwinRustAsyncSseMoi();

Future<void>  rustAutoOpaqueExplicitArgTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueExplicitArgTwinRustAsyncSseMoi(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueExplicitStructTwinRustAsyncSseMoi({required StructWithExplicitAutoOpaqueFieldTwinRustAsyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueExplicitStructTwinRustAsyncSseMoi(arg: arg);

Future<StructWithExplicitAutoOpaqueFieldTwinRustAsyncSseMoi>  rustAutoOpaqueExplicitReturnStructTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueExplicitReturnStructTwinRustAsyncSseMoi();

Future<NonCloneSimpleTwinRustAsyncSseMoi>  rustAutoOpaqueExplicitReturnTwinRustAsyncSseMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueExplicitReturnTwinRustAsyncSseMoi(initial: initial);

Future<int>  rustAutoOpaqueSleepTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi apple , required NonCloneSimpleTwinRustAsyncSseMoi orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueSleepTwinRustAsyncSseMoi(apple: apple, orange: orange);

Future<(OpaqueOneTwinRustAsyncSseMoi,OpaqueTwoTwinRustAsyncSseMoi)>  rustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsyncSseMoi();

Future<OpaqueTwoTwinRustAsyncSseMoi>  rustAutoOpaqueReturnOpaqueTwoTwinRustAsyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueReturnOpaqueTwoTwinRustAsyncSseMoi();

Future<int>  rustAutoOpaqueBorrowAndMutBorrowTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi borrow , required NonCloneSimpleTwinRustAsyncSseMoi mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueBorrowAndMutBorrowTwinRustAsyncSseMoi(borrow: borrow, mutBorrow: mutBorrow);

Future<int>  rustAutoOpaqueBorrowAndBorrowTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi a , required NonCloneSimpleTwinRustAsyncSseMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiRustAutoOpaqueBorrowAndBorrowTwinRustAsyncSseMoi(a: a, b: b);

            
                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinRustAsyncSseMoi + Send + Sync >>>
                abstract class BoxMyTraitTwinRustAsyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncSseMoi>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinRustAsyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinRustAsyncSseMoi>>
                abstract class NonCloneSimpleEnumTwinRustAsyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinRustAsyncSseMoi>>
                abstract class NonCloneSimpleTwinRustAsyncSseMoi implements RustOpaqueInterface {
                     Future<void>  instanceMethodArgBorrowTwinRustAsyncSseMoi();


 Future<void>  instanceMethodArgMutBorrowTwinRustAsyncSseMoi();


 Future<void>  instanceMethodArgOwnTwinRustAsyncSseMoi();


 Future<int> get instanceMethodGetterTwinRustAsyncSseMoi;


 Future<NonCloneSimpleTwinRustAsyncSseMoi>  instanceMethodReturnOwnTwinRustAsyncSseMoi();


/// named constructor
static Future<NonCloneSimpleTwinRustAsyncSseMoi>  newCustomNameTwinRustAsyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiNewCustomNameTwinRustAsyncSseMoi();


/// unnamed constructor
static Future<NonCloneSimpleTwinRustAsyncSseMoi>  newTwinRustAsyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiNewTwinRustAsyncSseMoi();


/// constructor with Result
static Future<NonCloneSimpleTwinRustAsyncSseMoi>  newWithResultTwinRustAsyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiNewWithResultTwinRustAsyncSseMoi();


static Future<void>  staticMethodArgBorrowTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiStaticMethodArgBorrowTwinRustAsyncSseMoi(arg: arg);


static Future<void>  staticMethodArgMutBorrowTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiStaticMethodArgMutBorrowTwinRustAsyncSseMoi(arg: arg);


static Future<void>  staticMethodArgOwnTwinRustAsyncSseMoi({required NonCloneSimpleTwinRustAsyncSseMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiStaticMethodArgOwnTwinRustAsyncSseMoi(arg: arg);


static Future<NonCloneSimpleTwinRustAsyncSseMoi>  staticMethodReturnOwnTwinRustAsyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncSseMoiNonCloneSimpleTwinRustAsyncSseMoiStaticMethodReturnOwnTwinRustAsyncSseMoi();



                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinRustAsyncSseMoi>>
                abstract class OpaqueOneTwinRustAsyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinRustAsyncSseMoi>>
                abstract class OpaqueTwoTwinRustAsyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncSseMoi>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsyncSseMoi implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinRustAsyncSseMoi {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinRustAsyncSseMoi with _$EnumWithGoodAndOpaqueTwinRustAsyncSseMoi  {
                    const EnumWithGoodAndOpaqueTwinRustAsyncSseMoi._();

                     const factory EnumWithGoodAndOpaqueTwinRustAsyncSseMoi.good(  String field0,) = EnumWithGoodAndOpaqueTwinRustAsyncSseMoi_Good;
 const factory EnumWithGoodAndOpaqueTwinRustAsyncSseMoi.opaque(  NonCloneSimpleTwinRustAsyncSseMoi field0,) = EnumWithGoodAndOpaqueTwinRustAsyncSseMoi_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinRustAsyncSseMoi  {
                final NonCloneSimpleTwinRustAsyncSseMoi autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinRustAsyncSseMoi({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinRustAsyncSseMoi &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinRustAsyncSseMoi  {
                final String good;
final NonCloneSimpleTwinRustAsyncSseMoi opaque;
final NonCloneSimpleTwinRustAsyncSseMoi? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinRustAsyncSseMoi({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinRustAsyncSseMoi &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            