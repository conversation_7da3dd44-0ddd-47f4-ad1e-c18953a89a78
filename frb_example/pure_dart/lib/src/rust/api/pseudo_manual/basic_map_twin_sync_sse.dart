// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sync_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Map<int, int>  exampleBasicMapTypeI8TwinSyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeI8TwinSyncSse(arg: arg);

Map<int, int>  exampleBasicMapTypeI16TwinSyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeI16TwinSyncSse(arg: arg);

Map<int, int>  exampleBasicMapTypeI32TwinSyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeI32TwinSyncSse(arg: arg);

Map<int, PlatformInt64>  exampleBasicMapTypeI64TwinSyncSse({required Map<int, PlatformInt64> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeI64TwinSyncSse(arg: arg);

Map<int, BigInt>  exampleBasicMapTypeI128TwinSyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeI128TwinSyncSse(arg: arg);

Map<int, int>  exampleBasicMapTypeU8TwinSyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeU8TwinSyncSse(arg: arg);

Map<int, int>  exampleBasicMapTypeU16TwinSyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeU16TwinSyncSse(arg: arg);

Map<int, int>  exampleBasicMapTypeU32TwinSyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeU32TwinSyncSse(arg: arg);

Map<int, BigInt>  exampleBasicMapTypeU64TwinSyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeU64TwinSyncSse(arg: arg);

Map<int, BigInt>  exampleBasicMapTypeU128TwinSyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeU128TwinSyncSse(arg: arg);

Map<int, PlatformInt64>  exampleBasicMapTypeIsizeTwinSyncSse({required Map<int, PlatformInt64> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeIsizeTwinSyncSse(arg: arg);

Map<int, BigInt>  exampleBasicMapTypeUsizeTwinSyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeUsizeTwinSyncSse(arg: arg);

Map<int, double>  exampleBasicMapTypeF32TwinSyncSse({required Map<int, double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeF32TwinSyncSse(arg: arg);

Map<int, double>  exampleBasicMapTypeF64TwinSyncSse({required Map<int, double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeF64TwinSyncSse(arg: arg);

Map<int, bool>  exampleBasicMapTypeBoolTwinSyncSse({required Map<int, bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeBoolTwinSyncSse(arg: arg);

Map<int, String>  exampleBasicMapTypeStringTwinSyncSse({required Map<int, String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeStringTwinSyncSse(arg: arg);

Map<int, Uint8List>  exampleBasicMapTypeBytesTwinSyncSse({required Map<int, Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeBytesTwinSyncSse(arg: arg);

Map<int, BasicPrimitiveEnumTwinSyncSse>  exampleBasicMapTypeBasicPrimitiveEnumTwinSyncSseTwinSyncSse({required Map<int, BasicPrimitiveEnumTwinSyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeBasicPrimitiveEnumTwinSyncSseTwinSyncSse(arg: arg);

Map<int, BasicGeneralEnumTwinSyncSse>  exampleBasicMapTypeBasicGeneralEnumTwinSyncSseTwinSyncSse({required Map<int, BasicGeneralEnumTwinSyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeBasicGeneralEnumTwinSyncSseTwinSyncSse(arg: arg);

Map<int, BasicStructTwinSyncSse>  exampleBasicMapTypeBasicStructTwinSyncSseTwinSyncSse({required Map<int, BasicStructTwinSyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinSyncSseExampleBasicMapTypeBasicStructTwinSyncSseTwinSyncSse(arg: arg);

            
            