// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_rust_async_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<int?>  exampleBasicOptionalTypeI8TwinRustAsyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeI8TwinRustAsyncSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeI16TwinRustAsyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeI16TwinRustAsyncSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeI32TwinRustAsyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeI32TwinRustAsyncSse(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeI64TwinRustAsyncSse({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeI64TwinRustAsyncSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeI128TwinRustAsyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeI128TwinRustAsyncSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeU8TwinRustAsyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeU8TwinRustAsyncSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeU16TwinRustAsyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeU16TwinRustAsyncSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeU32TwinRustAsyncSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeU32TwinRustAsyncSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU64TwinRustAsyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeU64TwinRustAsyncSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU128TwinRustAsyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeU128TwinRustAsyncSse(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeIsizeTwinRustAsyncSse({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeIsizeTwinRustAsyncSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeUsizeTwinRustAsyncSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeUsizeTwinRustAsyncSse(arg: arg);

Future<double?>  exampleBasicOptionalTypeF32TwinRustAsyncSse({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeF32TwinRustAsyncSse(arg: arg);

Future<double?>  exampleBasicOptionalTypeF64TwinRustAsyncSse({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeF64TwinRustAsyncSse(arg: arg);

Future<bool?>  exampleBasicOptionalTypeBoolTwinRustAsyncSse({bool? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeBoolTwinRustAsyncSse(arg: arg);

Future<String?>  exampleBasicOptionalTypeStringTwinRustAsyncSse({String? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeStringTwinRustAsyncSse(arg: arg);

Future<Uint8List?>  exampleBasicOptionalTypeBytesTwinRustAsyncSse({Uint8List? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeBytesTwinRustAsyncSse(arg: arg);

Future<BasicPrimitiveEnumTwinRustAsyncSse?>  exampleBasicOptionalTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse({BasicPrimitiveEnumTwinRustAsyncSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<BasicGeneralEnumTwinRustAsyncSse?>  exampleBasicOptionalTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse({BasicGeneralEnumTwinRustAsyncSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<BasicStructTwinRustAsyncSse?>  exampleBasicOptionalTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse({BasicStructTwinRustAsyncSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncSseExampleBasicOptionalTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

            
            