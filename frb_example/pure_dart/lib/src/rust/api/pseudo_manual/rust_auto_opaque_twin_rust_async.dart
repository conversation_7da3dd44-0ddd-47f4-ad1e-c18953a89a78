// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_rust_async.freezed.dart';

            

            Future<void>  rustAutoOpaqueArgOwnTwinRustAsync({required NonCloneSimpleTwinRustAsync arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueArgOwnTwinRustAsync(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgBorrowTwinRustAsync({required NonCloneSimpleTwinRustAsync arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueArgBorrowTwinRustAsync(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueArgMutBorrowTwinRustAsync({required NonCloneSimpleTwinRustAsync arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueArgMutBorrowTwinRustAsync(arg: arg, expect: expect, adder: adder);

Future<NonCloneSimpleTwinRustAsync>  rustAutoOpaqueReturnOwnTwinRustAsync({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueReturnOwnTwinRustAsync(initial: initial);

Future<NonCloneSimpleTwinRustAsync>  rustAutoOpaqueArgOwnAndReturnOwnTwinRustAsync({required NonCloneSimpleTwinRustAsync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueArgOwnAndReturnOwnTwinRustAsync(arg: arg);

Future<void>  rustAutoOpaqueTwoArgsTwinRustAsync({required NonCloneSimpleTwinRustAsync a , required NonCloneSimpleTwinRustAsync b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueTwoArgsTwinRustAsync(a: a, b: b);

Future<void>  rustAutoOpaqueNormalAndOpaqueArgTwinRustAsync({required NonCloneSimpleTwinRustAsync a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueNormalAndOpaqueArgTwinRustAsync(a: a, b: b);

/// "+" inside the type signature
Future<void>  rustAutoOpaquePlusSignArgTwinRustAsync({required BoxMyTraitTwinRustAsync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaquePlusSignArgTwinRustAsync(arg: arg);

Future<BoxMyTraitTwinRustAsync>  rustAutoOpaquePlusSignReturnTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaquePlusSignReturnTwinRustAsync();

Future<void>  rustAutoOpaqueCallableArgTwinRustAsync({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueCallableArgTwinRustAsync(arg: arg);

Future<BoxFnStringString>  rustAutoOpaqueCallableReturnTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueCallableReturnTwinRustAsync();

Future<void>  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsync({required StructWithGoodAndOpaqueFieldTwinRustAsync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinRustAsync(arg: arg);

Future<StructWithGoodAndOpaqueFieldTwinRustAsync>  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinRustAsync();

Future<void>  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsync({required EnumWithGoodAndOpaqueTwinRustAsync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinRustAsync(arg: arg);

Future<EnumWithGoodAndOpaqueTwinRustAsync>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinRustAsync();

Future<EnumWithGoodAndOpaqueTwinRustAsync>  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinRustAsync();

Future<void>  rustAutoOpaqueDummyTwinRustAsync({required StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsync a , required EnumWithGoodAndOpaqueWithoutOptionTwinRustAsync b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueDummyTwinRustAsync(a: a, b: b);

Future<void>  rustAutoOpaqueEnumArgBorrowTwinRustAsync({required NonCloneSimpleEnumTwinRustAsync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueEnumArgBorrowTwinRustAsync(arg: arg);

Future<NonCloneSimpleEnumTwinRustAsync>  rustAutoOpaqueEnumReturnOwnTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueEnumReturnOwnTwinRustAsync();

Stream<NonCloneSimpleTwinRustAsync>  rustAutoOpaqueStreamSinkTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueStreamSinkTwinRustAsync();

Future<void>  rustAutoOpaqueArgVecOwnTwinRustAsync({required List<NonCloneSimpleTwinRustAsync> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueArgVecOwnTwinRustAsync(arg: arg, expect: expect);

Future<List<NonCloneSimpleTwinRustAsync>>  rustAutoOpaqueReturnVecOwnTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueReturnVecOwnTwinRustAsync();

Future<void>  rustAutoOpaqueExplicitArgTwinRustAsync({required NonCloneSimpleTwinRustAsync arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueExplicitArgTwinRustAsync(arg: arg, expect: expect);

Future<void>  rustAutoOpaqueExplicitStructTwinRustAsync({required StructWithExplicitAutoOpaqueFieldTwinRustAsync arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueExplicitStructTwinRustAsync(arg: arg);

Future<StructWithExplicitAutoOpaqueFieldTwinRustAsync>  rustAutoOpaqueExplicitReturnStructTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueExplicitReturnStructTwinRustAsync();

Future<NonCloneSimpleTwinRustAsync>  rustAutoOpaqueExplicitReturnTwinRustAsync({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueExplicitReturnTwinRustAsync(initial: initial);

Future<int>  rustAutoOpaqueSleepTwinRustAsync({required NonCloneSimpleTwinRustAsync apple , required NonCloneSimpleTwinRustAsync orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueSleepTwinRustAsync(apple: apple, orange: orange);

Future<(OpaqueOneTwinRustAsync,OpaqueTwoTwinRustAsync)>  rustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueReturnOpaqueOneAndTwoTwinRustAsync();

Future<OpaqueTwoTwinRustAsync>  rustAutoOpaqueReturnOpaqueTwoTwinRustAsync() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueReturnOpaqueTwoTwinRustAsync();

Future<int>  rustAutoOpaqueBorrowAndMutBorrowTwinRustAsync({required NonCloneSimpleTwinRustAsync borrow , required NonCloneSimpleTwinRustAsync mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueBorrowAndMutBorrowTwinRustAsync(borrow: borrow, mutBorrow: mutBorrow);

Future<int>  rustAutoOpaqueBorrowAndBorrowTwinRustAsync({required NonCloneSimpleTwinRustAsync a , required NonCloneSimpleTwinRustAsync b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncRustAutoOpaqueBorrowAndBorrowTwinRustAsync(a: a, b: b);

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinRustAsync + Send + Sync >>>
                abstract class BoxMyTraitTwinRustAsync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinRustAsync>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinRustAsync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinRustAsync>>
                abstract class NonCloneSimpleEnumTwinRustAsync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinRustAsync>>
                abstract class NonCloneSimpleTwinRustAsync implements RustOpaqueInterface {
                     Future<void>  instanceMethodArgBorrowTwinRustAsync();


 Future<void>  instanceMethodArgMutBorrowTwinRustAsync();


 Future<void>  instanceMethodArgOwnTwinRustAsync();


 Future<int> get instanceMethodGetterTwinRustAsync;


 Future<NonCloneSimpleTwinRustAsync>  instanceMethodReturnOwnTwinRustAsync();


/// named constructor
static Future<NonCloneSimpleTwinRustAsync>  newCustomNameTwinRustAsync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncNewCustomNameTwinRustAsync();


/// unnamed constructor
static Future<NonCloneSimpleTwinRustAsync>  newTwinRustAsync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncNewTwinRustAsync();


/// constructor with Result
static Future<NonCloneSimpleTwinRustAsync>  newWithResultTwinRustAsync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncNewWithResultTwinRustAsync();


static Future<void>  staticMethodArgBorrowTwinRustAsync({required NonCloneSimpleTwinRustAsync arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncStaticMethodArgBorrowTwinRustAsync(arg: arg);


static Future<void>  staticMethodArgMutBorrowTwinRustAsync({required NonCloneSimpleTwinRustAsync arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncStaticMethodArgMutBorrowTwinRustAsync(arg: arg);


static Future<void>  staticMethodArgOwnTwinRustAsync({required NonCloneSimpleTwinRustAsync arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncStaticMethodArgOwnTwinRustAsync(arg: arg);


static Future<NonCloneSimpleTwinRustAsync>  staticMethodReturnOwnTwinRustAsync()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinRustAsyncNonCloneSimpleTwinRustAsyncStaticMethodReturnOwnTwinRustAsync();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinRustAsync>>
                abstract class OpaqueOneTwinRustAsync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinRustAsync>>
                abstract class OpaqueTwoTwinRustAsync implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsync>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinRustAsync implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinRustAsync {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinRustAsync with _$EnumWithGoodAndOpaqueTwinRustAsync  {
                    const EnumWithGoodAndOpaqueTwinRustAsync._();

                     const factory EnumWithGoodAndOpaqueTwinRustAsync.good(  String field0,) = EnumWithGoodAndOpaqueTwinRustAsync_Good;
 const factory EnumWithGoodAndOpaqueTwinRustAsync.opaque(  NonCloneSimpleTwinRustAsync field0,) = EnumWithGoodAndOpaqueTwinRustAsync_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinRustAsync  {
                final NonCloneSimpleTwinRustAsync autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinRustAsync({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinRustAsync &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinRustAsync  {
                final String good;
final NonCloneSimpleTwinRustAsync opaque;
final NonCloneSimpleTwinRustAsync? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinRustAsync({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinRustAsync &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            