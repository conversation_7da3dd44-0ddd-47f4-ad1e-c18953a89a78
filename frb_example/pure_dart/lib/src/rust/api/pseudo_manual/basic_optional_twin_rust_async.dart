// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_rust_async.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<int?>  exampleBasicOptionalTypeI8TwinRustAsync({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeI8TwinRustAsync(arg: arg);

Future<int?>  exampleBasicOptionalTypeI16TwinRustAsync({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeI16TwinRustAsync(arg: arg);

Future<int?>  exampleBasicOptionalTypeI32TwinRustAsync({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeI32TwinRustAsync(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeI64TwinRustAsync({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeI64TwinRustAsync(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeI128TwinRustAsync({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeI128TwinRustAsync(arg: arg);

Future<int?>  exampleBasicOptionalTypeU8TwinRustAsync({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeU8TwinRustAsync(arg: arg);

Future<int?>  exampleBasicOptionalTypeU16TwinRustAsync({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeU16TwinRustAsync(arg: arg);

Future<int?>  exampleBasicOptionalTypeU32TwinRustAsync({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeU32TwinRustAsync(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU64TwinRustAsync({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeU64TwinRustAsync(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU128TwinRustAsync({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeU128TwinRustAsync(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeIsizeTwinRustAsync({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeIsizeTwinRustAsync(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeUsizeTwinRustAsync({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeUsizeTwinRustAsync(arg: arg);

Future<double?>  exampleBasicOptionalTypeF32TwinRustAsync({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeF32TwinRustAsync(arg: arg);

Future<double?>  exampleBasicOptionalTypeF64TwinRustAsync({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeF64TwinRustAsync(arg: arg);

Future<bool?>  exampleBasicOptionalTypeBoolTwinRustAsync({bool? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeBoolTwinRustAsync(arg: arg);

Future<String?>  exampleBasicOptionalTypeStringTwinRustAsync({String? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeStringTwinRustAsync(arg: arg);

Future<Uint8List?>  exampleBasicOptionalTypeBytesTwinRustAsync({Uint8List? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeBytesTwinRustAsync(arg: arg);

Future<BasicPrimitiveEnumTwinRustAsync?>  exampleBasicOptionalTypeBasicPrimitiveEnumTwinRustAsyncTwinRustAsync({BasicPrimitiveEnumTwinRustAsync? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeBasicPrimitiveEnumTwinRustAsyncTwinRustAsync(arg: arg);

Future<BasicGeneralEnumTwinRustAsync?>  exampleBasicOptionalTypeBasicGeneralEnumTwinRustAsyncTwinRustAsync({BasicGeneralEnumTwinRustAsync? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeBasicGeneralEnumTwinRustAsyncTwinRustAsync(arg: arg);

Future<BasicStructTwinRustAsync?>  exampleBasicOptionalTypeBasicStructTwinRustAsyncTwinRustAsync({BasicStructTwinRustAsync? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinRustAsyncExampleBasicOptionalTypeBasicStructTwinRustAsyncTwinRustAsync(arg: arg);

            
            