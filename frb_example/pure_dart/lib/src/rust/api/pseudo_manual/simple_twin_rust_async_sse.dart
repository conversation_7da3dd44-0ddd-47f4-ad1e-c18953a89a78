// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<int>  simpleAdderTwinRustAsyncSse({required int a , required int b }) => RustLib.instance.api.crateApiPseudoManualSimpleTwinRustAsyncSseSimpleAdderTwinRustAsyncSse(a: a, b: b);

            
            