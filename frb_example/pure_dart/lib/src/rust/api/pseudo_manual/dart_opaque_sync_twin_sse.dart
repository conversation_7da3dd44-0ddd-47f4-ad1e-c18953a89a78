// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Object  syncLoopbackTwinSse({required Object opaque }) => RustLib.instance.api.crateApiPseudoManualDartOpaqueSyncTwinSseSyncLoopbackTwinSse(opaque: opaque);

Object?  syncOptionLoopbackTwinSse({Object? opaque }) => RustLib.instance.api.crateApiPseudoManualDartOpaqueSyncTwinSseSyncOptionLoopbackTwinSse(opaque: opaque);

String  syncAcceptDartOpaqueTwinSse({required Object opaque }) => RustLib.instance.api.crateApiPseudoManualDartOpaqueSyncTwinSseSyncAcceptDartOpaqueTwinSse(opaque: opaque);

/// [DartWrapObject] can be safely retrieved on a dart thread.
String  unwrapDartOpaqueTwinSse({required Object opaque }) => RustLib.instance.api.crateApiPseudoManualDartOpaqueSyncTwinSseUnwrapDartOpaqueTwinSse(opaque: opaque);

Object?  syncOptionDartOpaqueTwinSse({required Object opaque }) => RustLib.instance.api.crateApiPseudoManualDartOpaqueSyncTwinSseSyncOptionDartOpaqueTwinSse(opaque: opaque);

            
            