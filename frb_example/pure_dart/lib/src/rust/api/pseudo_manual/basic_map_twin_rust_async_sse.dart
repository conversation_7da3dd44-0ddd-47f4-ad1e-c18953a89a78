// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_rust_async_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<Map<int, int>>  exampleBasicMapTypeI8TwinRustAsyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeI8TwinRustAsyncSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeI16TwinRustAsyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeI16TwinRustAsyncSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeI32TwinRustAsyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeI32TwinRustAsyncSse(arg: arg);

Future<Map<int, PlatformInt64>>  exampleBasicMapTypeI64TwinRustAsyncSse({required Map<int, PlatformInt64> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeI64TwinRustAsyncSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeI128TwinRustAsyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeI128TwinRustAsyncSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeU8TwinRustAsyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeU8TwinRustAsyncSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeU16TwinRustAsyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeU16TwinRustAsyncSse(arg: arg);

Future<Map<int, int>>  exampleBasicMapTypeU32TwinRustAsyncSse({required Map<int, int> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeU32TwinRustAsyncSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeU64TwinRustAsyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeU64TwinRustAsyncSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeU128TwinRustAsyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeU128TwinRustAsyncSse(arg: arg);

Future<Map<int, PlatformInt64>>  exampleBasicMapTypeIsizeTwinRustAsyncSse({required Map<int, PlatformInt64> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeIsizeTwinRustAsyncSse(arg: arg);

Future<Map<int, BigInt>>  exampleBasicMapTypeUsizeTwinRustAsyncSse({required Map<int, BigInt> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeUsizeTwinRustAsyncSse(arg: arg);

Future<Map<int, double>>  exampleBasicMapTypeF32TwinRustAsyncSse({required Map<int, double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeF32TwinRustAsyncSse(arg: arg);

Future<Map<int, double>>  exampleBasicMapTypeF64TwinRustAsyncSse({required Map<int, double> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeF64TwinRustAsyncSse(arg: arg);

Future<Map<int, bool>>  exampleBasicMapTypeBoolTwinRustAsyncSse({required Map<int, bool> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeBoolTwinRustAsyncSse(arg: arg);

Future<Map<int, String>>  exampleBasicMapTypeStringTwinRustAsyncSse({required Map<int, String> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeStringTwinRustAsyncSse(arg: arg);

Future<Map<int, Uint8List>>  exampleBasicMapTypeBytesTwinRustAsyncSse({required Map<int, Uint8List> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeBytesTwinRustAsyncSse(arg: arg);

Future<Map<int, BasicPrimitiveEnumTwinRustAsyncSse>>  exampleBasicMapTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse({required Map<int, BasicPrimitiveEnumTwinRustAsyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeBasicPrimitiveEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<Map<int, BasicGeneralEnumTwinRustAsyncSse>>  exampleBasicMapTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse({required Map<int, BasicGeneralEnumTwinRustAsyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeBasicGeneralEnumTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

Future<Map<int, BasicStructTwinRustAsyncSse>>  exampleBasicMapTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse({required Map<int, BasicStructTwinRustAsyncSse> arg }) => RustLib.instance.api.crateApiPseudoManualBasicMapTwinRustAsyncSseExampleBasicMapTypeBasicStructTwinRustAsyncSseTwinRustAsyncSse(arg: arg);

            
            