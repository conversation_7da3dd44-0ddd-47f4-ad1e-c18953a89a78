// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<StructWithZeroFieldTwinRustAsyncSse>  funcStructWithZeroFieldTwinRustAsyncSse({required StructWithZeroFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncStructWithZeroFieldTwinRustAsyncSse(arg: arg);

Future<StructWithOneFieldTwinRustAsyncSse>  funcStructWithOneFieldTwinRustAsyncSse({required StructWithOneFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncStructWithOneFieldTwinRustAsyncSse(arg: arg);

Future<StructWithTwoFieldTwinRustAsyncSse>  funcStructWithTwoFieldTwinRustAsyncSse({required StructWithTwoFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncStructWithTwoFieldTwinRustAsyncSse(arg: arg);

Future<TupleStructWithOneFieldTwinRustAsyncSse>  funcTupleStructWithOneFieldTwinRustAsyncSse({required TupleStructWithOneFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncTupleStructWithOneFieldTwinRustAsyncSse(arg: arg);

Future<TupleStructWithTwoFieldTwinRustAsyncSse>  funcTupleStructWithTwoFieldTwinRustAsyncSse({required TupleStructWithTwoFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncTupleStructWithTwoFieldTwinRustAsyncSse(arg: arg);

Future<StructWithFieldRenameTwinRustAsyncSse>  funcForStructWithFieldRenameTwinRustAsyncSse({required StructWithFieldRenameTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncForStructWithFieldRenameTwinRustAsyncSse(arg: arg);

Future<StructWithDartKeywordFieldTwinRustAsyncSse>  funcForStructWithDartKeywordFieldTwinRustAsyncSse({required StructWithDartKeywordFieldTwinRustAsyncSse arg }) => RustLib.instance.api.crateApiPseudoManualStructureTwinRustAsyncSseFuncForStructWithDartKeywordFieldTwinRustAsyncSse(arg: arg);

            class StructWithDartKeywordFieldTwinRustAsyncSse  {
                final int class_;
final PlatformInt64 interface_;

                const StructWithDartKeywordFieldTwinRustAsyncSse({required this.class_ ,required this.interface_ ,});

                
                

                
        @override
        int get hashCode => class_.hashCode^interface_.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithDartKeywordFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && class_ == other.class_&& interface_ == other.interface_;
        
            }

class StructWithFieldRenameTwinRustAsyncSse  {
                final int renamed_field;

                const StructWithFieldRenameTwinRustAsyncSse({required this.renamed_field ,});

                
                

                
        @override
        int get hashCode => renamed_field.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithFieldRenameTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && renamed_field == other.renamed_field;
        
            }

class StructWithOneFieldTwinRustAsyncSse  {
                final int a;

                const StructWithOneFieldTwinRustAsyncSse({required this.a ,});

                
                

                
        @override
        int get hashCode => a.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithOneFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && a == other.a;
        
            }

class StructWithTwoFieldTwinRustAsyncSse  {
                final int a;
final int b;

                const StructWithTwoFieldTwinRustAsyncSse({required this.a ,required this.b ,});

                
                

                
        @override
        int get hashCode => a.hashCode^b.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithTwoFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && a == other.a&& b == other.b;
        
            }

class StructWithZeroFieldTwinRustAsyncSse  {
                

                const StructWithZeroFieldTwinRustAsyncSse();

                
                

                
        @override
        int get hashCode => 0;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithZeroFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                ;
        
            }

class TupleStructWithOneFieldTwinRustAsyncSse  {
                final int field0;

                const TupleStructWithOneFieldTwinRustAsyncSse({required this.field0 ,});

                
                

                
        @override
        int get hashCode => field0.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is TupleStructWithOneFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && field0 == other.field0;
        
            }

class TupleStructWithTwoFieldTwinRustAsyncSse  {
                final int field0;
final int field1;

                const TupleStructWithTwoFieldTwinRustAsyncSse({required this.field0 ,required this.field1 ,});

                
                

                
        @override
        int get hashCode => field0.hashCode^field1.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is TupleStructWithTwoFieldTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && field0 == other.field0&& field1 == other.field1;
        
            }
            