// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_sync_sse.freezed.dart';

            

            void  rustAutoOpaqueArgOwnTwinSyncSse({required NonCloneSimpleTwinSyncSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueArgOwnTwinSyncSse(arg: arg, expect: expect);

void  rustAutoOpaqueArgBorrowTwinSyncSse({required NonCloneSimpleTwinSyncSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueArgBorrowTwinSyncSse(arg: arg, expect: expect);

void  rustAutoOpaqueArgMutBorrowTwinSyncSse({required NonCloneSimpleTwinSyncSse arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueArgMutBorrowTwinSyncSse(arg: arg, expect: expect, adder: adder);

NonCloneSimpleTwinSyncSse  rustAutoOpaqueReturnOwnTwinSyncSse({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueReturnOwnTwinSyncSse(initial: initial);

NonCloneSimpleTwinSyncSse  rustAutoOpaqueArgOwnAndReturnOwnTwinSyncSse({required NonCloneSimpleTwinSyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueArgOwnAndReturnOwnTwinSyncSse(arg: arg);

void  rustAutoOpaqueTwoArgsTwinSyncSse({required NonCloneSimpleTwinSyncSse a , required NonCloneSimpleTwinSyncSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueTwoArgsTwinSyncSse(a: a, b: b);

void  rustAutoOpaqueNormalAndOpaqueArgTwinSyncSse({required NonCloneSimpleTwinSyncSse a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueNormalAndOpaqueArgTwinSyncSse(a: a, b: b);

/// "+" inside the type signature
void  rustAutoOpaquePlusSignArgTwinSyncSse({required BoxMyTraitTwinSyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaquePlusSignArgTwinSyncSse(arg: arg);

BoxMyTraitTwinSyncSse  rustAutoOpaquePlusSignReturnTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaquePlusSignReturnTwinSyncSse();

void  rustAutoOpaqueCallableArgTwinSyncSse({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueCallableArgTwinSyncSse(arg: arg);

BoxFnStringString  rustAutoOpaqueCallableReturnTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueCallableReturnTwinSyncSse();

void  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSyncSse({required StructWithGoodAndOpaqueFieldTwinSyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSyncSse(arg: arg);

StructWithGoodAndOpaqueFieldTwinSyncSse  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSyncSse();

void  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSyncSse({required EnumWithGoodAndOpaqueTwinSyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSyncSse(arg: arg);

EnumWithGoodAndOpaqueTwinSyncSse  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSyncSse();

EnumWithGoodAndOpaqueTwinSyncSse  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSyncSse();

void  rustAutoOpaqueDummyTwinSyncSse({required StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncSse a , required EnumWithGoodAndOpaqueWithoutOptionTwinSyncSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueDummyTwinSyncSse(a: a, b: b);

void  rustAutoOpaqueEnumArgBorrowTwinSyncSse({required NonCloneSimpleEnumTwinSyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueEnumArgBorrowTwinSyncSse(arg: arg);

NonCloneSimpleEnumTwinSyncSse  rustAutoOpaqueEnumReturnOwnTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueEnumReturnOwnTwinSyncSse();

Stream<NonCloneSimpleTwinSyncSse>  rustAutoOpaqueStreamSinkTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueStreamSinkTwinSyncSse();

void  rustAutoOpaqueArgVecOwnTwinSyncSse({required List<NonCloneSimpleTwinSyncSse> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueArgVecOwnTwinSyncSse(arg: arg, expect: expect);

List<NonCloneSimpleTwinSyncSse>  rustAutoOpaqueReturnVecOwnTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueReturnVecOwnTwinSyncSse();

void  rustAutoOpaqueExplicitArgTwinSyncSse({required NonCloneSimpleTwinSyncSse arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueExplicitArgTwinSyncSse(arg: arg, expect: expect);

void  rustAutoOpaqueExplicitStructTwinSyncSse({required StructWithExplicitAutoOpaqueFieldTwinSyncSse arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueExplicitStructTwinSyncSse(arg: arg);

StructWithExplicitAutoOpaqueFieldTwinSyncSse  rustAutoOpaqueExplicitReturnStructTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueExplicitReturnStructTwinSyncSse();

NonCloneSimpleTwinSyncSse  rustAutoOpaqueExplicitReturnTwinSyncSse({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueExplicitReturnTwinSyncSse(initial: initial);

int  rustAutoOpaqueSleepTwinSyncSse({required NonCloneSimpleTwinSyncSse apple , required NonCloneSimpleTwinSyncSse orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueSleepTwinSyncSse(apple: apple, orange: orange);

(OpaqueOneTwinSyncSse,OpaqueTwoTwinSyncSse)  rustAutoOpaqueReturnOpaqueOneAndTwoTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueReturnOpaqueOneAndTwoTwinSyncSse();

OpaqueTwoTwinSyncSse  rustAutoOpaqueReturnOpaqueTwoTwinSyncSse() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueReturnOpaqueTwoTwinSyncSse();

int  rustAutoOpaqueBorrowAndMutBorrowTwinSyncSse({required NonCloneSimpleTwinSyncSse borrow , required NonCloneSimpleTwinSyncSse mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueBorrowAndMutBorrowTwinSyncSse(borrow: borrow, mutBorrow: mutBorrow);

int  rustAutoOpaqueBorrowAndBorrowTwinSyncSse({required NonCloneSimpleTwinSyncSse a , required NonCloneSimpleTwinSyncSse b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseRustAutoOpaqueBorrowAndBorrowTwinSyncSse(a: a, b: b);

            
                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinSyncSse + Send + Sync >>>
                abstract class BoxMyTraitTwinSyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinSyncSse>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinSyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinSyncSse>>
                abstract class NonCloneSimpleEnumTwinSyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinSyncSse>>
                abstract class NonCloneSimpleTwinSyncSse implements RustOpaqueInterface {
                     void  instanceMethodArgBorrowTwinSyncSse();


 void  instanceMethodArgMutBorrowTwinSyncSse();


 void  instanceMethodArgOwnTwinSyncSse();


 int get instanceMethodGetterTwinSyncSse;


 NonCloneSimpleTwinSyncSse  instanceMethodReturnOwnTwinSyncSse();


/// named constructor
static NonCloneSimpleTwinSyncSse  newCustomNameTwinSyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseNewCustomNameTwinSyncSse();


/// unnamed constructor
static NonCloneSimpleTwinSyncSse  newTwinSyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseNewTwinSyncSse();


/// constructor with Result
static NonCloneSimpleTwinSyncSse  newWithResultTwinSyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseNewWithResultTwinSyncSse();


static void  staticMethodArgBorrowTwinSyncSse({required NonCloneSimpleTwinSyncSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseStaticMethodArgBorrowTwinSyncSse(arg: arg);


static void  staticMethodArgMutBorrowTwinSyncSse({required NonCloneSimpleTwinSyncSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseStaticMethodArgMutBorrowTwinSyncSse(arg: arg);


static void  staticMethodArgOwnTwinSyncSse({required NonCloneSimpleTwinSyncSse arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseStaticMethodArgOwnTwinSyncSse(arg: arg);


static NonCloneSimpleTwinSyncSse  staticMethodReturnOwnTwinSyncSse()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseNonCloneSimpleTwinSyncSseStaticMethodReturnOwnTwinSyncSse();



                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinSyncSse>>
                abstract class OpaqueOneTwinSyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinSyncSse>>
                abstract class OpaqueTwoTwinSyncSse implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueNom<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncSse>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncSse implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinSyncSse {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinSyncSse with _$EnumWithGoodAndOpaqueTwinSyncSse  {
                    const EnumWithGoodAndOpaqueTwinSyncSse._();

                     const factory EnumWithGoodAndOpaqueTwinSyncSse.good(  String field0,) = EnumWithGoodAndOpaqueTwinSyncSse_Good;
 const factory EnumWithGoodAndOpaqueTwinSyncSse.opaque(  NonCloneSimpleTwinSyncSse field0,) = EnumWithGoodAndOpaqueTwinSyncSse_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinSyncSse  {
                final NonCloneSimpleTwinSyncSse autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinSyncSse({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinSyncSse &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinSyncSse  {
                final String good;
final NonCloneSimpleTwinSyncSse opaque;
final NonCloneSimpleTwinSyncSse? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinSyncSse({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinSyncSse &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            