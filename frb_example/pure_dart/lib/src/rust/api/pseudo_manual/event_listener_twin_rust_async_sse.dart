// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
part 'event_listener_twin_rust_async_sse.freezed.dart';

            // These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `clone`, `deref`, `initialize`


            Future<Stream<EventTwinRustAsyncSse>>  registerEventListenerTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualEventListenerTwinRustAsyncSseRegisterEventListenerTwinRustAsyncSse();

Future<void>  closeEventListenerTwinRustAsyncSse() => RustLib.instance.api.crateApiPseudoManualEventListenerTwinRustAsyncSseCloseEventListenerTwinRustAsyncSse();

Future<void>  createEventTwinRustAsyncSse({required String address , required String payload }) => RustLib.instance.api.crateApiPseudoManualEventListenerTwinRustAsyncSseCreateEventTwinRustAsyncSse(address: address, payload: payload);

            @freezed
sealed class EventTwinRustAsyncSse with _$EventTwinRustAsyncSse  {
                const EventTwinRustAsyncSse._();
                const factory EventTwinRustAsyncSse({ required  String address, required  String payload,}) = _EventTwinRustAsyncSse;
                 Future<String>  asStringTwinRustAsyncSse()=>RustLib.instance.api.crateApiPseudoManualEventListenerTwinRustAsyncSseEventTwinRustAsyncSseAsStringTwinRustAsyncSse(that: this, );


                
                
            }
            