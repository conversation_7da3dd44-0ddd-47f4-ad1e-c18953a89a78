// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:freezed_annotation/freezed_annotation.dart' hide protected;
import 'rust_auto_opaque_twin_moi.dart';
part 'rust_auto_opaque_twin_sync_sse_moi.freezed.dart';

            

            void  rustAutoOpaqueArgOwnTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueArgOwnTwinSyncSseMoi(arg: arg, expect: expect);

void  rustAutoOpaqueArgBorrowTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueArgBorrowTwinSyncSseMoi(arg: arg, expect: expect);

void  rustAutoOpaqueArgMutBorrowTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg , required int expect , required int adder }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueArgMutBorrowTwinSyncSseMoi(arg: arg, expect: expect, adder: adder);

NonCloneSimpleTwinSyncSseMoi  rustAutoOpaqueReturnOwnTwinSyncSseMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueReturnOwnTwinSyncSseMoi(initial: initial);

NonCloneSimpleTwinSyncSseMoi  rustAutoOpaqueArgOwnAndReturnOwnTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueArgOwnAndReturnOwnTwinSyncSseMoi(arg: arg);

void  rustAutoOpaqueTwoArgsTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi a , required NonCloneSimpleTwinSyncSseMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueTwoArgsTwinSyncSseMoi(a: a, b: b);

void  rustAutoOpaqueNormalAndOpaqueArgTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi a , required String b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueNormalAndOpaqueArgTwinSyncSseMoi(a: a, b: b);

/// "+" inside the type signature
void  rustAutoOpaquePlusSignArgTwinSyncSseMoi({required BoxMyTraitTwinSyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaquePlusSignArgTwinSyncSseMoi(arg: arg);

BoxMyTraitTwinSyncSseMoi  rustAutoOpaquePlusSignReturnTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaquePlusSignReturnTwinSyncSseMoi();

void  rustAutoOpaqueCallableArgTwinSyncSseMoi({required BoxFnStringString arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueCallableArgTwinSyncSseMoi(arg: arg);

BoxFnStringString  rustAutoOpaqueCallableReturnTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueCallableReturnTwinSyncSseMoi();

void  rustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSyncSseMoi({required StructWithGoodAndOpaqueFieldTwinSyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldArgOwnTwinSyncSseMoi(arg: arg);

StructWithGoodAndOpaqueFieldTwinSyncSseMoi  rustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueStructWithGoodAndOpaqueFieldReturnOwnTwinSyncSseMoi();

void  rustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSyncSseMoi({required EnumWithGoodAndOpaqueTwinSyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueEnumWithGoodAndOpaqueArgOwnTwinSyncSseMoi(arg: arg);

EnumWithGoodAndOpaqueTwinSyncSseMoi  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnGoodTwinSyncSseMoi();

EnumWithGoodAndOpaqueTwinSyncSseMoi  rustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueEnumWithGoodAndOpaqueReturnOwnOpaqueTwinSyncSseMoi();

void  rustAutoOpaqueDummyTwinSyncSseMoi({required StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncSseMoi a , required EnumWithGoodAndOpaqueWithoutOptionTwinSyncSseMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueDummyTwinSyncSseMoi(a: a, b: b);

void  rustAutoOpaqueEnumArgBorrowTwinSyncSseMoi({required NonCloneSimpleEnumTwinSyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueEnumArgBorrowTwinSyncSseMoi(arg: arg);

NonCloneSimpleEnumTwinSyncSseMoi  rustAutoOpaqueEnumReturnOwnTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueEnumReturnOwnTwinSyncSseMoi();

Stream<NonCloneSimpleTwinSyncSseMoi>  rustAutoOpaqueStreamSinkTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueStreamSinkTwinSyncSseMoi();

void  rustAutoOpaqueArgVecOwnTwinSyncSseMoi({required List<NonCloneSimpleTwinSyncSseMoi> arg , required List<int> expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueArgVecOwnTwinSyncSseMoi(arg: arg, expect: expect);

List<NonCloneSimpleTwinSyncSseMoi>  rustAutoOpaqueReturnVecOwnTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueReturnVecOwnTwinSyncSseMoi();

void  rustAutoOpaqueExplicitArgTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg , required int expect }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueExplicitArgTwinSyncSseMoi(arg: arg, expect: expect);

void  rustAutoOpaqueExplicitStructTwinSyncSseMoi({required StructWithExplicitAutoOpaqueFieldTwinSyncSseMoi arg }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueExplicitStructTwinSyncSseMoi(arg: arg);

StructWithExplicitAutoOpaqueFieldTwinSyncSseMoi  rustAutoOpaqueExplicitReturnStructTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueExplicitReturnStructTwinSyncSseMoi();

NonCloneSimpleTwinSyncSseMoi  rustAutoOpaqueExplicitReturnTwinSyncSseMoi({required int initial }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueExplicitReturnTwinSyncSseMoi(initial: initial);

int  rustAutoOpaqueSleepTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi apple , required NonCloneSimpleTwinSyncSseMoi orange }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueSleepTwinSyncSseMoi(apple: apple, orange: orange);

(OpaqueOneTwinSyncSseMoi,OpaqueTwoTwinSyncSseMoi)  rustAutoOpaqueReturnOpaqueOneAndTwoTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueReturnOpaqueOneAndTwoTwinSyncSseMoi();

OpaqueTwoTwinSyncSseMoi  rustAutoOpaqueReturnOpaqueTwoTwinSyncSseMoi() => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueReturnOpaqueTwoTwinSyncSseMoi();

int  rustAutoOpaqueBorrowAndMutBorrowTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi borrow , required NonCloneSimpleTwinSyncSseMoi mutBorrow }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueBorrowAndMutBorrowTwinSyncSseMoi(borrow: borrow, mutBorrow: mutBorrow);

int  rustAutoOpaqueBorrowAndBorrowTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi a , required NonCloneSimpleTwinSyncSseMoi b }) => RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiRustAutoOpaqueBorrowAndBorrowTwinSyncSseMoi(a: a, b: b);

            
                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<Box < dyn MyTraitTwinSyncSseMoi + Send + Sync >>>
                abstract class BoxMyTraitTwinSyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<EnumWithGoodAndOpaqueWithoutOptionTwinSyncSseMoi>>
                abstract class EnumWithGoodAndOpaqueWithoutOptionTwinSyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleEnumTwinSyncSseMoi>>
                abstract class NonCloneSimpleEnumTwinSyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<NonCloneSimpleTwinSyncSseMoi>>
                abstract class NonCloneSimpleTwinSyncSseMoi implements RustOpaqueInterface {
                     void  instanceMethodArgBorrowTwinSyncSseMoi();


 void  instanceMethodArgMutBorrowTwinSyncSseMoi();


 void  instanceMethodArgOwnTwinSyncSseMoi();


 int get instanceMethodGetterTwinSyncSseMoi;


 NonCloneSimpleTwinSyncSseMoi  instanceMethodReturnOwnTwinSyncSseMoi();


/// named constructor
static NonCloneSimpleTwinSyncSseMoi  newCustomNameTwinSyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiNewCustomNameTwinSyncSseMoi();


/// unnamed constructor
static NonCloneSimpleTwinSyncSseMoi  newTwinSyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiNewTwinSyncSseMoi();


/// constructor with Result
static NonCloneSimpleTwinSyncSseMoi  newWithResultTwinSyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiNewWithResultTwinSyncSseMoi();


static void  staticMethodArgBorrowTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiStaticMethodArgBorrowTwinSyncSseMoi(arg: arg);


static void  staticMethodArgMutBorrowTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiStaticMethodArgMutBorrowTwinSyncSseMoi(arg: arg);


static void  staticMethodArgOwnTwinSyncSseMoi({required NonCloneSimpleTwinSyncSseMoi arg })=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiStaticMethodArgOwnTwinSyncSseMoi(arg: arg);


static NonCloneSimpleTwinSyncSseMoi  staticMethodReturnOwnTwinSyncSseMoi()=>RustLib.instance.api.crateApiPseudoManualRustAutoOpaqueTwinSyncSseMoiNonCloneSimpleTwinSyncSseMoiStaticMethodReturnOwnTwinSyncSseMoi();



                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueOneTwinSyncSseMoi>>
                abstract class OpaqueOneTwinSyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<OpaqueTwoTwinSyncSseMoi>>
                abstract class OpaqueTwoTwinSyncSseMoi implements RustOpaqueInterface {
                    

                    
                }
                


                // Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncSseMoi>>
                abstract class StructWithGoodAndOpaqueFieldWithoutOptionTwinSyncSseMoi implements RustOpaqueInterface {
                     String get good;


  set good(String good);



                    
                }
                


                abstract class MyTraitTwinSyncSseMoi {
                     Future<void>  f();


                }
                

@freezed
                sealed class EnumWithGoodAndOpaqueTwinSyncSseMoi with _$EnumWithGoodAndOpaqueTwinSyncSseMoi  {
                    const EnumWithGoodAndOpaqueTwinSyncSseMoi._();

                     const factory EnumWithGoodAndOpaqueTwinSyncSseMoi.good(  String field0,) = EnumWithGoodAndOpaqueTwinSyncSseMoi_Good;
 const factory EnumWithGoodAndOpaqueTwinSyncSseMoi.opaque(  NonCloneSimpleTwinSyncSseMoi field0,) = EnumWithGoodAndOpaqueTwinSyncSseMoi_Opaque;

                    

                    
                }

class StructWithExplicitAutoOpaqueFieldTwinSyncSseMoi  {
                final NonCloneSimpleTwinSyncSseMoi autoOpaque;
final int normal;

                const StructWithExplicitAutoOpaqueFieldTwinSyncSseMoi({required this.autoOpaque ,required this.normal ,});

                
                

                
        @override
        int get hashCode => autoOpaque.hashCode^normal.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithExplicitAutoOpaqueFieldTwinSyncSseMoi &&
                runtimeType == other.runtimeType
                && autoOpaque == other.autoOpaque&& normal == other.normal;
        
            }

class StructWithGoodAndOpaqueFieldTwinSyncSseMoi  {
                final String good;
final NonCloneSimpleTwinSyncSseMoi opaque;
final NonCloneSimpleTwinSyncSseMoi? optionOpaque;

                const StructWithGoodAndOpaqueFieldTwinSyncSseMoi({required this.good ,required this.opaque ,this.optionOpaque ,});

                
                

                
        @override
        int get hashCode => good.hashCode^opaque.hashCode^optionOpaque.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is StructWithGoodAndOpaqueFieldTwinSyncSseMoi &&
                runtimeType == other.runtimeType
                && good == other.good&& opaque == other.opaque&& optionOpaque == other.optionOpaque;
        
            }
            