// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sse.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<int?>  exampleBasicOptionalTypeI8TwinSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeI8TwinSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeI16TwinSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeI16TwinSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeI32TwinSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeI32TwinSse(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeI64TwinSse({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeI64TwinSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeI128TwinSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeI128TwinSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeU8TwinSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeU8TwinSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeU16TwinSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeU16TwinSse(arg: arg);

Future<int?>  exampleBasicOptionalTypeU32TwinSse({int? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeU32TwinSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU64TwinSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeU64TwinSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeU128TwinSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeU128TwinSse(arg: arg);

Future<PlatformInt64?>  exampleBasicOptionalTypeIsizeTwinSse({PlatformInt64? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeIsizeTwinSse(arg: arg);

Future<BigInt?>  exampleBasicOptionalTypeUsizeTwinSse({BigInt? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeUsizeTwinSse(arg: arg);

Future<double?>  exampleBasicOptionalTypeF32TwinSse({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeF32TwinSse(arg: arg);

Future<double?>  exampleBasicOptionalTypeF64TwinSse({double? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeF64TwinSse(arg: arg);

Future<bool?>  exampleBasicOptionalTypeBoolTwinSse({bool? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeBoolTwinSse(arg: arg);

Future<String?>  exampleBasicOptionalTypeStringTwinSse({String? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeStringTwinSse(arg: arg);

Future<Uint8List?>  exampleBasicOptionalTypeBytesTwinSse({Uint8List? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeBytesTwinSse(arg: arg);

Future<BasicPrimitiveEnumTwinSse?>  exampleBasicOptionalTypeBasicPrimitiveEnumTwinSseTwinSse({BasicPrimitiveEnumTwinSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeBasicPrimitiveEnumTwinSseTwinSse(arg: arg);

Future<BasicGeneralEnumTwinSse?>  exampleBasicOptionalTypeBasicGeneralEnumTwinSseTwinSse({BasicGeneralEnumTwinSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeBasicGeneralEnumTwinSseTwinSse(arg: arg);

Future<BasicStructTwinSse?>  exampleBasicOptionalTypeBasicStructTwinSseTwinSse({BasicStructTwinSse? arg }) => RustLib.instance.api.crateApiPseudoManualBasicOptionalTwinSseExampleBasicOptionalTypeBasicStructTwinSseTwinSse(arg: arg);

            
            