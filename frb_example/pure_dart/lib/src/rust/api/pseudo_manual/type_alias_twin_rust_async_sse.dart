// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../auxiliary/sample_types.dart';
import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            

            Future<BigInt>  handleTypeAliasIdTwinRustAsyncSse({required BigInt input }) => RustLib.instance.api.crateApiPseudoManualTypeAliasTwinRustAsyncSseHandleTypeAliasIdTwinRustAsyncSse(input: input);

Future<BigInt>  handleTypeNestAliasIdTwinRustAsyncSse({required BigInt input }) => RustLib.instance.api.crateApiPseudoManualTypeAliasTwinRustAsyncSseHandleTypeNestAliasIdTwinRustAsyncSse(input: input);

Future<TestModelTwinRustAsyncSse>  handleTypeAliasModelTwinRustAsyncSse({required BigInt input }) => RustLib.instance.api.crateApiPseudoManualTypeAliasTwinRustAsyncSseHandleTypeAliasModelTwinRustAsyncSse(input: input);

            class TestModelTwinRustAsyncSse  {
                final BigInt id;
final String name;
final MyEnum aliasEnum;
final MyStruct aliasStruct;

                const TestModelTwinRustAsyncSse({required this.id ,required this.name ,required this.aliasEnum ,required this.aliasStruct ,});

                
                

                
        @override
        int get hashCode => id.hashCode^name.hashCode^aliasEnum.hashCode^aliasStruct.hashCode;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is TestModelTwinRustAsyncSse &&
                runtimeType == other.runtimeType
                && id == other.id&& name == other.name&& aliasEnum == other.aliasEnum&& aliasStruct == other.aliasStruct;
        
            }
            