// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../auxiliary/sample_types.dart';
import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';


            // These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `clone`, `fmt`


            Future<void>  funcReturnUnitTwinNormal() => RustLib.instance.api.crateApiMiscTypeFuncReturnUnitTwinNormal();

Future<List<MySize>>  handleListOfStructTwinNormal({required List<MySize> l }) => RustLib.instance.api.crateApiMiscTypeHandleListOfStructTwinNormal(l: l);

Future<List<String>>  handleStringListTwinNormal({required List<String> names }) => RustLib.instance.api.crateApiMiscTypeHandleStringListTwinNormal(names: names);

Future<EmptyTwinNormal>  emptyStructTwinNormal({required EmptyTwinNormal empty }) => RustLib.instance.api.crateApiMiscTypeEmptyStructTwinNormal(empty: empty);

            class EmptyTwinNormal  {
                

                const EmptyTwinNormal();

                
                

                
        @override
        int get hashCode => 0;
        

                
        @override
        bool operator ==(Object other) =>
            identical(this, other) ||
            other is EmptyTwinNormal &&
                runtimeType == other.runtimeType
                ;
        
            }
            