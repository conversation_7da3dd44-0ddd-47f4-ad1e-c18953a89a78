// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Object syncLoopbackTwinNormal({required Object opaque}) => RustLib.instance.api
    .crateApiDartOpaqueSyncSyncLoopbackTwinNormal(opaque: opaque);

Object? syncOptionLoopbackTwinNormal({Object? opaque}) => RustLib.instance.api
    .crateApiDartOpaqueSyncSyncOptionLoopbackTwinNormal(opaque: opaque);

String syncAcceptDartOpaqueTwinNormal({required Object opaque}) =>
    RustLib.instance.api
        .crateApiDartOpaqueSyncSyncAcceptDartOpaqueTwinNormal(opaque: opaque);

/// [DartWrapObject] can be safely retrieved on a dart thread.
String unwrapDartOpaqueTwinNormal({required Object opaque}) =>
    RustLib.instance.api
        .crateApiDartOpaqueSyncUnwrapDartOpaqueTwinNormal(opaque: opaque);

Object? syncOptionDartOpaqueTwinNormal({required Object opaque}) =>
    RustLib.instance.api
        .crateApiDartOpaqueSyncSyncOptionDartOpaqueTwinNormal(opaque: opaque);
