// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../auxiliary/sample_types.dart';
import '../../frb_generated.dart';
import 'enumeration_twin_sync.dart';
import 'misc_example_twin_sync.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `build_hasher`, `clone`, `fmt`, `hash_one`
// These functions are ignored (category: IgnoreBecauseOwnerTyShouldIgnore): `default`

Map<int, int> funcHashMapI32I32TwinSync({required Map<int, int> arg}) => RustLib
    .instance.api
    .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapI32I32TwinSync(arg: arg);

Set<int> funcHashSetI32TwinSync({required Set<int> arg}) => RustLib.instance.api
    .crateApiPseudoManualMapAndSetTwinSyncFuncHashSetI32TwinSync(arg: arg);

Map<String, String> funcHashMapStringStringTwinSync(
        {required Map<String, String> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapStringStringTwinSync(
            arg: arg);

Map<String, String> funcHashMapStringStringHasherTwinSync(
        {required Map<String, String> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapStringStringHasherTwinSync(
            arg: arg);

Set<String> funcHashSetStringTwinSync({required Set<String> arg}) => RustLib
    .instance.api
    .crateApiPseudoManualMapAndSetTwinSyncFuncHashSetStringTwinSync(arg: arg);

Set<String> funcHashSetStringHasherTwinSync({required Set<String> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashSetStringHasherTwinSync(
            arg: arg);

Map<String, Uint8List> funcHashMapStringBytesTwinSync(
        {required Map<String, Uint8List> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapStringBytesTwinSync(
            arg: arg);

Map<String, MySize> funcHashMapStringStructTwinSync(
        {required Map<String, MySize> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapStringStructTwinSync(
            arg: arg);

Map<String, EnumSimpleTwinSync> funcHashMapStringSimpleEnumTwinSync(
        {required Map<String, EnumSimpleTwinSync> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapStringSimpleEnumTwinSync(
            arg: arg);

Map<String, KitchenSinkTwinSync> funcHashMapStringComplexEnumTwinSync(
        {required Map<String, KitchenSinkTwinSync> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualMapAndSetTwinSyncFuncHashMapStringComplexEnumTwinSync(
            arg: arg);

// Rust type: RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<CustomHasherTwinSync>>
abstract class CustomHasherTwinSync implements RustOpaqueInterface {}
