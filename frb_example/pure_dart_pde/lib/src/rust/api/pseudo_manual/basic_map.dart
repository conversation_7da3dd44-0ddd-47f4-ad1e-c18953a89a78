// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Future<Map<int, int>> exampleBasicMapTypeI8TwinNormal(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeI8TwinNormal(arg: arg);

Future<Map<int, int>> exampleBasicMapTypeI16TwinNormal(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeI16TwinNormal(arg: arg);

Future<Map<int, int>> exampleBasicMapTypeI32TwinNormal(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeI32TwinNormal(arg: arg);

Future<Map<int, PlatformInt64>> exampleBasicMapTypeI64TwinNormal(
        {required Map<int, PlatformInt64> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeI64TwinNormal(arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeI128TwinNormal(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeI128TwinNormal(
            arg: arg);

Future<Map<int, int>> exampleBasicMapTypeU8TwinNormal(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeU8TwinNormal(arg: arg);

Future<Map<int, int>> exampleBasicMapTypeU16TwinNormal(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeU16TwinNormal(arg: arg);

Future<Map<int, int>> exampleBasicMapTypeU32TwinNormal(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeU32TwinNormal(arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeU64TwinNormal(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeU64TwinNormal(arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeU128TwinNormal(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeU128TwinNormal(
            arg: arg);

Future<Map<int, PlatformInt64>> exampleBasicMapTypeIsizeTwinNormal(
        {required Map<int, PlatformInt64> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeIsizeTwinNormal(
            arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeUsizeTwinNormal(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeUsizeTwinNormal(
            arg: arg);

Future<Map<int, double>> exampleBasicMapTypeF32TwinNormal(
        {required Map<int, double> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeF32TwinNormal(arg: arg);

Future<Map<int, double>> exampleBasicMapTypeF64TwinNormal(
        {required Map<int, double> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeF64TwinNormal(arg: arg);

Future<Map<int, bool>> exampleBasicMapTypeBoolTwinNormal(
        {required Map<int, bool> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeBoolTwinNormal(
            arg: arg);

Future<Map<int, String>> exampleBasicMapTypeStringTwinNormal(
        {required Map<int, String> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeStringTwinNormal(
            arg: arg);

Future<Map<int, Uint8List>> exampleBasicMapTypeBytesTwinNormal(
        {required Map<int, Uint8List> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapExampleBasicMapTypeBytesTwinNormal(
            arg: arg);

Future<Map<int, BasicPrimitiveEnumTwinNormal>>
    exampleBasicMapTypeBasicPrimitiveEnumTwinNormalTwinNormal(
            {required Map<int, BasicPrimitiveEnumTwinNormal> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapExampleBasicMapTypeBasicPrimitiveEnumTwinNormalTwinNormal(
                arg: arg);

Future<Map<int, BasicGeneralEnumTwinNormal>>
    exampleBasicMapTypeBasicGeneralEnumTwinNormalTwinNormal(
            {required Map<int, BasicGeneralEnumTwinNormal> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapExampleBasicMapTypeBasicGeneralEnumTwinNormalTwinNormal(
                arg: arg);

Future<Map<int, BasicStructTwinNormal>>
    exampleBasicMapTypeBasicStructTwinNormalTwinNormal(
            {required Map<int, BasicStructTwinNormal> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapExampleBasicMapTypeBasicStructTwinNormalTwinNormal(
                arg: arg);
