// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sync.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Map<int, int> exampleBasicMapTypeI8TwinSync({required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeI8TwinSync(
            arg: arg);

Map<int, int> exampleBasicMapTypeI16TwinSync({required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeI16TwinSync(
            arg: arg);

Map<int, int> exampleBasicMapTypeI32TwinSync({required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeI32TwinSync(
            arg: arg);

Map<int, PlatformInt64> exampleBasicMapTypeI64TwinSync(
        {required Map<int, PlatformInt64> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeI64TwinSync(
            arg: arg);

Map<int, BigInt> exampleBasicMapTypeI128TwinSync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeI128TwinSync(
            arg: arg);

Map<int, int> exampleBasicMapTypeU8TwinSync({required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeU8TwinSync(
            arg: arg);

Map<int, int> exampleBasicMapTypeU16TwinSync({required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeU16TwinSync(
            arg: arg);

Map<int, int> exampleBasicMapTypeU32TwinSync({required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeU32TwinSync(
            arg: arg);

Map<int, BigInt> exampleBasicMapTypeU64TwinSync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeU64TwinSync(
            arg: arg);

Map<int, BigInt> exampleBasicMapTypeU128TwinSync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeU128TwinSync(
            arg: arg);

Map<int, PlatformInt64> exampleBasicMapTypeIsizeTwinSync(
        {required Map<int, PlatformInt64> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeIsizeTwinSync(
            arg: arg);

Map<int, BigInt> exampleBasicMapTypeUsizeTwinSync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeUsizeTwinSync(
            arg: arg);

Map<int, double> exampleBasicMapTypeF32TwinSync(
        {required Map<int, double> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeF32TwinSync(
            arg: arg);

Map<int, double> exampleBasicMapTypeF64TwinSync(
        {required Map<int, double> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeF64TwinSync(
            arg: arg);

Map<int, bool> exampleBasicMapTypeBoolTwinSync({required Map<int, bool> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeBoolTwinSync(
            arg: arg);

Map<int, String> exampleBasicMapTypeStringTwinSync(
        {required Map<int, String> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeStringTwinSync(
            arg: arg);

Map<int, Uint8List> exampleBasicMapTypeBytesTwinSync(
        {required Map<int, Uint8List> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeBytesTwinSync(
            arg: arg);

Map<int, BasicPrimitiveEnumTwinSync>
    exampleBasicMapTypeBasicPrimitiveEnumTwinSyncTwinSync(
            {required Map<int, BasicPrimitiveEnumTwinSync> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeBasicPrimitiveEnumTwinSyncTwinSync(
                arg: arg);

Map<int,
    BasicGeneralEnumTwinSync> exampleBasicMapTypeBasicGeneralEnumTwinSyncTwinSync(
        {required Map<int, BasicGeneralEnumTwinSync> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeBasicGeneralEnumTwinSyncTwinSync(
            arg: arg);

Map<int, BasicStructTwinSync> exampleBasicMapTypeBasicStructTwinSyncTwinSync(
        {required Map<int, BasicStructTwinSync> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinSyncExampleBasicMapTypeBasicStructTwinSyncTwinSync(
            arg: arg);
