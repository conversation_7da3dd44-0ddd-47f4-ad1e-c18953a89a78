// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_sync.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

int? exampleBasicOptionalTypeI8TwinSync({int? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeI8TwinSync(
        arg: arg);

int? exampleBasicOptionalTypeI16TwinSync({int? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeI16TwinSync(
        arg: arg);

int? exampleBasicOptionalTypeI32TwinSync({int? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeI32TwinSync(
        arg: arg);

PlatformInt64? exampleBasicOptionalTypeI64TwinSync({PlatformInt64? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeI64TwinSync(
        arg: arg);

BigInt? exampleBasicOptionalTypeI128TwinSync({BigInt? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeI128TwinSync(
        arg: arg);

int? exampleBasicOptionalTypeU8TwinSync({int? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeU8TwinSync(
        arg: arg);

int? exampleBasicOptionalTypeU16TwinSync({int? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeU16TwinSync(
        arg: arg);

int? exampleBasicOptionalTypeU32TwinSync({int? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeU32TwinSync(
        arg: arg);

BigInt? exampleBasicOptionalTypeU64TwinSync({BigInt? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeU64TwinSync(
        arg: arg);

BigInt? exampleBasicOptionalTypeU128TwinSync({BigInt? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeU128TwinSync(
        arg: arg);

PlatformInt64? exampleBasicOptionalTypeIsizeTwinSync({PlatformInt64? arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeIsizeTwinSync(
            arg: arg);

BigInt? exampleBasicOptionalTypeUsizeTwinSync({BigInt? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeUsizeTwinSync(
        arg: arg);

double? exampleBasicOptionalTypeF32TwinSync({double? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeF32TwinSync(
        arg: arg);

double? exampleBasicOptionalTypeF64TwinSync({double? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeF64TwinSync(
        arg: arg);

bool? exampleBasicOptionalTypeBoolTwinSync({bool? arg}) => RustLib.instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeBoolTwinSync(
        arg: arg);

String? exampleBasicOptionalTypeStringTwinSync({String? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeStringTwinSync(
        arg: arg);

Uint8List? exampleBasicOptionalTypeBytesTwinSync({Uint8List? arg}) => RustLib
    .instance.api
    .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeBytesTwinSync(
        arg: arg);

BasicPrimitiveEnumTwinSync?
    exampleBasicOptionalTypeBasicPrimitiveEnumTwinSyncTwinSync(
            {BasicPrimitiveEnumTwinSync? arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeBasicPrimitiveEnumTwinSyncTwinSync(
                arg: arg);

BasicGeneralEnumTwinSync? exampleBasicOptionalTypeBasicGeneralEnumTwinSyncTwinSync(
        {BasicGeneralEnumTwinSync? arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeBasicGeneralEnumTwinSyncTwinSync(
            arg: arg);

BasicStructTwinSync? exampleBasicOptionalTypeBasicStructTwinSyncTwinSync(
        {BasicStructTwinSync? arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicOptionalTwinSyncExampleBasicOptionalTypeBasicStructTwinSyncTwinSync(
            arg: arg);
