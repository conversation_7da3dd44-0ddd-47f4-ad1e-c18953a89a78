// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

import 'dart:io';

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'basic_twin_rust_async.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Future<Map<int, int>> exampleBasicMapTypeI8TwinRustAsync(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeI8TwinRustAsync(
            arg: arg);

Future<Map<int, int>> exampleBasicMapTypeI16TwinRustAsync(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeI16TwinRustAsync(
            arg: arg);

Future<Map<int, int>> exampleBasicMapTypeI32TwinRustAsync(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeI32TwinRustAsync(
            arg: arg);

Future<Map<int, PlatformInt64>> exampleBasicMapTypeI64TwinRustAsync(
        {required Map<int, PlatformInt64> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeI64TwinRustAsync(
            arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeI128TwinRustAsync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeI128TwinRustAsync(
            arg: arg);

Future<Map<int, int>> exampleBasicMapTypeU8TwinRustAsync(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeU8TwinRustAsync(
            arg: arg);

Future<Map<int, int>> exampleBasicMapTypeU16TwinRustAsync(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeU16TwinRustAsync(
            arg: arg);

Future<Map<int, int>> exampleBasicMapTypeU32TwinRustAsync(
        {required Map<int, int> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeU32TwinRustAsync(
            arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeU64TwinRustAsync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeU64TwinRustAsync(
            arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeU128TwinRustAsync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeU128TwinRustAsync(
            arg: arg);

Future<Map<int, PlatformInt64>> exampleBasicMapTypeIsizeTwinRustAsync(
        {required Map<int, PlatformInt64> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeIsizeTwinRustAsync(
            arg: arg);

Future<Map<int, BigInt>> exampleBasicMapTypeUsizeTwinRustAsync(
        {required Map<int, BigInt> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeUsizeTwinRustAsync(
            arg: arg);

Future<Map<int, double>> exampleBasicMapTypeF32TwinRustAsync(
        {required Map<int, double> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeF32TwinRustAsync(
            arg: arg);

Future<Map<int, double>> exampleBasicMapTypeF64TwinRustAsync(
        {required Map<int, double> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeF64TwinRustAsync(
            arg: arg);

Future<Map<int, bool>> exampleBasicMapTypeBoolTwinRustAsync(
        {required Map<int, bool> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeBoolTwinRustAsync(
            arg: arg);

Future<Map<int, String>> exampleBasicMapTypeStringTwinRustAsync(
        {required Map<int, String> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeStringTwinRustAsync(
            arg: arg);

Future<Map<int, Uint8List>> exampleBasicMapTypeBytesTwinRustAsync(
        {required Map<int, Uint8List> arg}) =>
    RustLib.instance.api
        .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeBytesTwinRustAsync(
            arg: arg);

Future<Map<int, BasicPrimitiveEnumTwinRustAsync>>
    exampleBasicMapTypeBasicPrimitiveEnumTwinRustAsyncTwinRustAsync(
            {required Map<int, BasicPrimitiveEnumTwinRustAsync> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeBasicPrimitiveEnumTwinRustAsyncTwinRustAsync(
                arg: arg);

Future<Map<int, BasicGeneralEnumTwinRustAsync>>
    exampleBasicMapTypeBasicGeneralEnumTwinRustAsyncTwinRustAsync(
            {required Map<int, BasicGeneralEnumTwinRustAsync> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeBasicGeneralEnumTwinRustAsyncTwinRustAsync(
                arg: arg);

Future<Map<int, BasicStructTwinRustAsync>>
    exampleBasicMapTypeBasicStructTwinRustAsyncTwinRustAsync(
            {required Map<int, BasicStructTwinRustAsync> arg}) =>
        RustLib.instance.api
            .crateApiPseudoManualBasicMapTwinRustAsyncExampleBasicMapTypeBasicStructTwinRustAsyncTwinRustAsync(
                arg: arg);
